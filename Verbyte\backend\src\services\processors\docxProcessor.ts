import fs from 'fs';
import path from 'path';
import mammoth from 'mammoth';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  DOCXMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class DOCXProcessor implements IDocumentProcessor {
  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
           mimeType === 'application/msword' ||
           mimeType.includes('word') ||
           mimeType.includes('docx');
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.DOCX;
  }

  /**
   * Process a DOCX file and extract text content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing DOCX file: ${filePath}`);

      // Read the DOCX file
      const buffer = fs.readFileSync(filePath);

      // Configure mammoth options
      const mammothOptions = {
        convertImage: mammoth.images.imgElement((image: any) => {
          // Handle images if needed
          return image.read("base64").then((imageBuffer: any) => {
            return {
              src: `data:${image.contentType};base64,${imageBuffer}`
            };
          });
        }),
        includeDefaultStyleMap: true,
        includeEmbeddedStyleMap: true
      };

      // Extract text and HTML
      const [textResult, htmlResult] = await Promise.all([
        mammoth.extractRawText({ buffer }),
        options?.preserveFormatting ? mammoth.convertToHtml({ buffer }, mammothOptions) : Promise.resolve(null)
      ]);

      // Extract metadata
      const metadata = await this.extractMetadata(filePath, textResult.value, htmlResult?.value);

      // Create chunks if requested
      const chunks = options?.chunkSize ? 
        this.createChunks(textResult.value, options.chunkSize, options.chunkOverlap || 0) : 
        undefined;

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(textResult.value);
      metadata.characterCount = textResult.value.length;

      const result: TextExtractionResult = {
        text: textResult.value,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      // Log any warnings from mammoth
      if (textResult.messages.length > 0) {
        logger.warn(`DOCX processing warnings for ${filePath}:`, textResult.messages);
      }

      logger.debug(`DOCX processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown DOCX processing error';
      logger.error(`DOCX processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Extract metadata from DOCX document
   */
  private async extractMetadata(filePath: string, text: string, html?: string): Promise<DOCXMetadata> {
    try {
      const stats = fs.statSync(filePath);

      // Analyze content for metadata
      const hasImages = html ? html.includes('<img') : false;
      const hasHeaders = this.hasHeaders(text);
      const hasFooters = this.hasFooters(text);
      const hasTables = html ? html.includes('<table') : this.detectTables(text);

      const metadata: DOCXMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        size: stats.size,
        type: DocumentType.DOCX,
        hasImages,
        hasHeaders,
        hasFooters,
        hasTables,
        styleCount: html ? this.countStyles(html) : 0,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract DOCX metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Create text chunks from extracted text
   */
  private createChunks(text: string, chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];
    let currentPos = 0;

    while (currentPos < text.length) {
      const endPos = Math.min(currentPos + chunkSize, text.length);
      const chunkText = text.substring(currentPos, endPos);

      chunks.push({
        id: `docx_chunk_${chunks.length}`,
        text: chunkText,
        startIndex: currentPos,
        endIndex: endPos,
        metadata: {
          source: 'docx',
          chunkIndex: chunks.length
        }
      });

      // Move to next chunk with overlap
      currentPos = endPos - overlap;
      if (currentPos >= text.length) break;
    }

    return chunks;
  }

  /**
   * Detect if document has headers
   */
  private hasHeaders(text: string): boolean {
    // Look for common header patterns
    const headerPatterns = [
      /^[A-Z][A-Z\s]{2,}$/m, // ALL CAPS lines
      /^\d+\.\s+[A-Z]/m,     // Numbered sections
      /^Chapter\s+\d+/mi,    // Chapter headings
      /^Section\s+\d+/mi,    // Section headings
      /^[A-Z][a-z]+\s+[A-Z][a-z]+$/m // Title Case lines
    ];

    return headerPatterns.some(pattern => pattern.test(text));
  }

  /**
   * Detect if document has footers
   */
  private hasFooters(text: string): boolean {
    // Look for common footer patterns
    const footerPatterns = [
      /Page\s+\d+/i,
      /\d+\s*$/m,
      /©\s*\d{4}/,
      /Copyright/i
    ];

    return footerPatterns.some(pattern => pattern.test(text));
  }

  /**
   * Detect tables in text
   */
  private detectTables(text: string): boolean {
    // Look for table-like patterns
    const tablePatterns = [
      /\t.*\t.*\t/,           // Tab-separated values
      /\|.*\|.*\|/,           // Pipe-separated values
      /^\s*\w+\s+\w+\s+\w+/m  // Space-separated columns
    ];

    return tablePatterns.some(pattern => pattern.test(text));
  }

  /**
   * Count styles in HTML
   */
  private countStyles(html: string): number {
    const styleMatches = html.match(/style="[^"]*"/g);
    return styleMatches ? styleMatches.length : 0;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): DOCXMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      size: stats.size,
      type: DocumentType.DOCX,
      hasImages: false,
      hasHeaders: false,
      hasFooters: false,
      hasTables: false,
      styleCount: 0,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): DOCXMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
