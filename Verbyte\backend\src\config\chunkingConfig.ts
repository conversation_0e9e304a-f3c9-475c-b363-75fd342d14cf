import { ChunkingStrategy, ChunkingConfig, TextPreprocessingOptions } from '@/types/document';

/**
 * Chunking Configuration Service
 * 
 * Manages configuration for text chunking parameters with environment variable support
 * and document-type specific defaults.
 */
export class ChunkingConfigService {
  private static instance: ChunkingConfigService;

  // Environment variable keys
  private static readonly ENV_KEYS = {
    DEFAULT_CHUNK_SIZE: 'CHUNKING_DEFAULT_CHUNK_SIZE',
    DEFAULT_CHUNK_OVERLAP: 'CHUNKING_DEFAULT_CHUNK_OVERLAP',
    DEFAULT_STRATEGY: 'CHUNKING_DEFAULT_STRATEGY',
    MIN_CHUNK_SIZE: 'CHUNKING_MIN_CHUNK_SIZE',
    MAX_CHUNK_SIZE: 'CHUNKING_MAX_CHUNK_SIZE',
    SENTENCE_WINDOW_SIZE: 'CHUNKING_SENTENCE_WINDOW_SIZE',
    RESPECT_WORD_BOUNDARIES: 'CHUNKING_RESPECT_WORD_BOUNDARIES',
    PRESERVE_FORMATTING: 'CHUNKING_PRESERVE_FORMATTING',
    NORMALIZE_WHITESPACE: 'PREPROCESSING_NORMALIZE_WHITESPACE',
    REMOVE_CONTROL_CHARS: 'PREPROCESSING_REMOVE_CONTROL_CHARS'
  };

  // Default configurations for different document types
  private static readonly DOCUMENT_TYPE_CONFIGS: Record<string, Partial<ChunkingConfig>> = {
    pdf: {
      strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
      chunkSize: 1000,
      chunkOverlap: 200,
      respectWordBoundaries: true,
      preserveFormatting: true,
      separators: ['\n\n', '\n', '. ', ' ', '']
    },
    docx: {
      strategy: ChunkingStrategy.PARAGRAPH,
      chunkSize: 1200,
      chunkOverlap: 150,
      preserveFormatting: true,
      respectWordBoundaries: true
    },
    txt: {
      strategy: ChunkingStrategy.PARAGRAPH,
      chunkSize: 1000,
      chunkOverlap: 200,
      respectWordBoundaries: true,
      preserveFormatting: true
    },
    markdown: {
      strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
      chunkSize: 1000,
      chunkOverlap: 100,
      separators: ['\n## ', '\n### ', '\n#### ', '\n\n', '\n', '. ', ' ', ''],
      preserveFormatting: true,
      respectWordBoundaries: true
    },
    csv: {
      strategy: ChunkingStrategy.CHARACTER,
      chunkSize: 2000,
      chunkOverlap: 100,
      respectWordBoundaries: false,
      preserveFormatting: false
    },
    json: {
      strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
      chunkSize: 1500,
      chunkOverlap: 100,
      separators: ['},\n', '},', '\n', ' ', ''],
      preserveFormatting: true,
      respectWordBoundaries: false
    },
    powerpoint: {
      strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
      chunkSize: 800,
      chunkOverlap: 100,
      separators: ['\n---\n', '\n\n', '\n', '. ', ' ', ''],
      preserveFormatting: true,
      respectWordBoundaries: true
    },
    image: {
      strategy: ChunkingStrategy.SENTENCE,
      chunkSize: 800,
      chunkOverlap: 150,
      sentenceWindowSize: 3,
      respectWordBoundaries: true,
      preserveFormatting: false
    }
  };

  // Default preprocessing options for different document types
  private static readonly PREPROCESSING_CONFIGS: Record<string, Partial<TextPreprocessingOptions>> = {
    pdf: {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: true,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    },
    docx: {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: false,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    },
    txt: {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: false,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    },
    markdown: {
      normalizeWhitespace: false, // Preserve markdown formatting
      removeExtraSpaces: false,
      normalizeUnicode: false,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    },
    csv: {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: false,
      preserveLineBreaks: false,
      removeControlCharacters: true,
      trimChunks: true
    },
    json: {
      normalizeWhitespace: false, // Preserve JSON structure
      removeExtraSpaces: false,
      normalizeUnicode: false,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    },
    powerpoint: {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: true,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    },
    image: {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: true,
      preserveLineBreaks: false,
      removeControlCharacters: true,
      trimChunks: true
    }
  };

  public static getInstance(): ChunkingConfigService {
    if (!ChunkingConfigService.instance) {
      ChunkingConfigService.instance = new ChunkingConfigService();
    }
    return ChunkingConfigService.instance;
  }

  /**
   * Get default chunking configuration
   */
  public getDefaultConfig(): ChunkingConfig {
    return {
      strategy: this.getEnumFromEnv(ChunkingConfigService.ENV_KEYS.DEFAULT_STRATEGY, ChunkingStrategy.RECURSIVE_CHARACTER),
      chunkSize: this.getNumberFromEnv(ChunkingConfigService.ENV_KEYS.DEFAULT_CHUNK_SIZE, 1000),
      chunkOverlap: this.getNumberFromEnv(ChunkingConfigService.ENV_KEYS.DEFAULT_CHUNK_OVERLAP, 200),
      separators: ['\n\n', '\n', '. ', ' ', ''],
      keepSeparator: false,
      lengthFunction: (text: string) => text.length,
      sentenceWindowSize: this.getNumberFromEnv(ChunkingConfigService.ENV_KEYS.SENTENCE_WINDOW_SIZE, 3),
      preserveFormatting: this.getBooleanFromEnv(ChunkingConfigService.ENV_KEYS.PRESERVE_FORMATTING, true),
      respectWordBoundaries: this.getBooleanFromEnv(ChunkingConfigService.ENV_KEYS.RESPECT_WORD_BOUNDARIES, true),
      minChunkSize: this.getNumberFromEnv(ChunkingConfigService.ENV_KEYS.MIN_CHUNK_SIZE, 50),
      maxChunkSize: this.getNumberFromEnv(ChunkingConfigService.ENV_KEYS.MAX_CHUNK_SIZE, 4000)
    };
  }

  /**
   * Get default preprocessing options
   */
  public getDefaultPreprocessingOptions(): TextPreprocessingOptions {
    return {
      normalizeWhitespace: this.getBooleanFromEnv(ChunkingConfigService.ENV_KEYS.NORMALIZE_WHITESPACE, true),
      removeExtraSpaces: true,
      normalizeUnicode: false,
      preserveLineBreaks: true,
      removeControlCharacters: this.getBooleanFromEnv(ChunkingConfigService.ENV_KEYS.REMOVE_CONTROL_CHARS, true),
      trimChunks: true
    };
  }

  /**
   * Get configuration for a specific document type
   */
  public getConfigForDocumentType(
    documentType: string,
    overrides: Partial<ChunkingConfig> = {}
  ): ChunkingConfig {
    const defaultConfig = this.getDefaultConfig();
    const typeConfig = ChunkingConfigService.DOCUMENT_TYPE_CONFIGS[documentType.toLowerCase()] || {};
    
    return {
      ...defaultConfig,
      ...typeConfig,
      ...overrides
    };
  }

  /**
   * Get preprocessing options for a specific document type
   */
  public getPreprocessingForDocumentType(
    documentType: string,
    overrides: Partial<TextPreprocessingOptions> = {}
  ): TextPreprocessingOptions {
    const defaultOptions = this.getDefaultPreprocessingOptions();
    const typeOptions = ChunkingConfigService.PREPROCESSING_CONFIGS[documentType.toLowerCase()] || {};
    
    return {
      ...defaultOptions,
      ...typeOptions,
      ...overrides
    };
  }

  /**
   * Get configuration optimized for a specific use case
   */
  public getConfigForUseCase(useCase: string): ChunkingConfig {
    const defaultConfig = this.getDefaultConfig();

    switch (useCase.toLowerCase()) {
      case 'search':
        return {
          ...defaultConfig,
          strategy: ChunkingStrategy.SENTENCE,
          chunkSize: 500,
          chunkOverlap: 100,
          sentenceWindowSize: 2
        };
      
      case 'summarization':
        return {
          ...defaultConfig,
          strategy: ChunkingStrategy.PARAGRAPH,
          chunkSize: 2000,
          chunkOverlap: 300
        };
      
      case 'qa':
        return {
          ...defaultConfig,
          strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
          chunkSize: 1000,
          chunkOverlap: 200
        };
      
      case 'embedding':
        return {
          ...defaultConfig,
          strategy: ChunkingStrategy.SENTENCE,
          chunkSize: 800,
          chunkOverlap: 150,
          sentenceWindowSize: 3
        };
      
      case 'analysis':
        return {
          ...defaultConfig,
          strategy: ChunkingStrategy.PARAGRAPH,
          chunkSize: 1500,
          chunkOverlap: 250
        };
      
      default:
        return defaultConfig;
    }
  }

  /**
   * Validate chunking configuration
   */
  public validateConfig(config: ChunkingConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (config.chunkSize <= 0) {
      errors.push('Chunk size must be greater than 0');
    }

    if (config.chunkOverlap < 0) {
      errors.push('Chunk overlap cannot be negative');
    }

    if (config.chunkOverlap >= config.chunkSize) {
      errors.push('Chunk overlap must be less than chunk size');
    }

    if (config.minChunkSize && config.minChunkSize > config.chunkSize) {
      errors.push('Minimum chunk size cannot be greater than chunk size');
    }

    if (config.maxChunkSize && config.maxChunkSize < config.chunkSize) {
      errors.push('Maximum chunk size cannot be less than chunk size');
    }

    if (config.sentenceWindowSize && config.sentenceWindowSize <= 0) {
      errors.push('Sentence window size must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get number from environment variable with fallback
   */
  private getNumberFromEnv(key: string, defaultValue: number): number {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * Get boolean from environment variable with fallback
   */
  private getBooleanFromEnv(key: string, defaultValue: boolean): boolean {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    
    return value.toLowerCase() === 'true';
  }

  /**
   * Get enum value from environment variable with fallback
   */
  private getEnumFromEnv<T>(key: string, defaultValue: T): T {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    
    // Check if the value exists in the enum
    const enumValues = Object.values(ChunkingStrategy);
    return enumValues.includes(value as any) ? (value as any) : defaultValue;
  }
}
