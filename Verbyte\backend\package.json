{"name": "verbyte-backend", "version": "1.0.0", "description": "Verbyte backend API server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "migrate": "node dist/scripts/migrate.js", "seed": "node dist/scripts/seed.js"}, "dependencies": {"@huggingface/inference": "^4.6.1", "@qdrant/js-client-rest": "^1.7.0", "@types/express-validator": "^2.20.33", "@types/redis": "^4.0.10", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mammoth": "^1.6.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "office-text-extractor": "^3.0.3", "officegen": "^0.6.5", "ollama": "^0.5.16", "openai": "^5.11.0", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.4.54", "redis": "^5.6.1", "tesseract.js": "^6.0.1", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/papaparse": "^5.3.14", "@types/pdf-parse": "^1.1.4", "@types/pdfjs-dist": "^2.10.377", "@types/supertest": "^2.0.16", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.0"}}