/**
 * Embedding Cache Service
 * Provides caching for embeddings to improve performance and reduce API costs
 */

import crypto from 'crypto';
import { logger } from '../../utils/logger';
import { EmbeddingCacheEntry } from '../../types/document';

export class EmbeddingCache {
  private memoryCache: Map<string, EmbeddingCacheEntry>;
  private cacheStats: { hits: number; misses: number };
  private readonly maxMemoryCacheSize: number;
  private readonly ttlMs: number; // Time to live in milliseconds

  constructor(maxSize: number = 10000, ttlHours: number = 24) {
    this.memoryCache = new Map();
    this.cacheStats = { hits: 0, misses: 0 };
    this.maxMemoryCacheSize = maxSize;
    this.ttlMs = ttlHours * 60 * 60 * 1000;
    
    // Clean up expired entries every hour
    setInterval(() => this.cleanupExpired(), 60 * 60 * 1000);
    
    logger.debug(`Initialized embedding cache with max size: ${maxSize}, TTL: ${ttlHours}h`);
  }

  /**
   * Generate a cache key for a text and model combination
   */
  private generateCacheKey(text: string, model: string): string {
    const hash = crypto.createHash('sha256');
    hash.update(`${text}:${model}`);
    return hash.digest('hex');
  }

  /**
   * Check if a cache entry is expired
   */
  private isExpired(entry: EmbeddingCacheEntry): boolean {
    return Date.now() - entry.timestamp.getTime() > this.ttlMs;
  }

  /**
   * Get embedding from cache
   */
  async get(text: string, model: string): Promise<number[] | null> {
    const key = this.generateCacheKey(text, model);
    const entry = this.memoryCache.get(key);

    if (!entry) {
      this.cacheStats.misses++;
      return null;
    }

    if (this.isExpired(entry)) {
      this.memoryCache.delete(key);
      this.cacheStats.misses++;
      return null;
    }

    this.cacheStats.hits++;
    logger.debug(`Cache hit for text length: ${text.length}, model: ${model}`);
    return entry.embedding;
  }

  /**
   * Store embedding in cache
   */
  async set(text: string, embedding: number[], model: string, dimension: number): Promise<void> {
    const key = this.generateCacheKey(text, model);
    
    // Check if we need to make space
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      this.evictOldest();
    }

    const entry: EmbeddingCacheEntry = {
      embedding,
      timestamp: new Date(),
      model,
      dimension
    };

    this.memoryCache.set(key, entry);
    logger.debug(`Cached embedding for text length: ${text.length}, model: ${model}, dimension: ${dimension}`);
  }

  /**
   * Remove oldest entries to make space
   */
  private evictOldest(): void {
    const entriesToRemove = Math.floor(this.maxMemoryCacheSize * 0.1); // Remove 10%
    const entries = Array.from(this.memoryCache.entries());
    
    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a[1].timestamp.getTime() - b[1].timestamp.getTime());
    
    for (let i = 0; i < entriesToRemove && i < entries.length; i++) {
      this.memoryCache.delete(entries[i][0]);
    }
    
    logger.debug(`Evicted ${entriesToRemove} oldest cache entries`);
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpired(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp.getTime() > this.ttlMs) {
        this.memoryCache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      logger.debug(`Cleaned up ${removedCount} expired cache entries`);
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    this.memoryCache.clear();
    this.cacheStats = { hits: 0, misses: 0 };
    logger.debug('Embedding cache cleared');
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{ hits: number; misses: number; size: number }> {
    return {
      hits: this.cacheStats.hits,
      misses: this.cacheStats.misses,
      size: this.memoryCache.size
    };
  }

  /**
   * Get cache hit rate
   */
  getHitRate(): number {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    return total > 0 ? this.cacheStats.hits / total : 0;
  }

  /**
   * Get memory usage information
   */
  getMemoryInfo(): { 
    currentSize: number; 
    maxSize: number; 
    utilizationPercent: number;
    averageEntrySize: number;
  } {
    const currentSize = this.memoryCache.size;
    const utilizationPercent = (currentSize / this.maxMemoryCacheSize) * 100;
    
    // Calculate average entry size (rough estimate)
    let totalDimensions = 0;
    for (const entry of this.memoryCache.values()) {
      totalDimensions += entry.dimension;
    }
    const averageEntrySize = currentSize > 0 ? totalDimensions / currentSize : 0;

    return {
      currentSize,
      maxSize: this.maxMemoryCacheSize,
      utilizationPercent,
      averageEntrySize
    };
  }

  /**
   * Preload embeddings into cache (useful for frequently used texts)
   */
  async preload(textEmbeddingPairs: Array<{ text: string; embedding: number[]; model: string; dimension: number }>): Promise<void> {
    for (const pair of textEmbeddingPairs) {
      await this.set(pair.text, pair.embedding, pair.model, pair.dimension);
    }
    logger.info(`Preloaded ${textEmbeddingPairs.length} embeddings into cache`);
  }

  /**
   * Export cache contents (for backup/restore)
   */
  exportCache(): Array<{ key: string; entry: EmbeddingCacheEntry }> {
    return Array.from(this.memoryCache.entries()).map(([key, entry]) => ({ key, entry }));
  }

  /**
   * Import cache contents (for backup/restore)
   */
  importCache(data: Array<{ key: string; entry: EmbeddingCacheEntry }>): void {
    this.memoryCache.clear();
    for (const { key, entry } of data) {
      if (!this.isExpired(entry)) {
        this.memoryCache.set(key, entry);
      }
    }
    logger.info(`Imported ${this.memoryCache.size} valid cache entries`);
  }
}
