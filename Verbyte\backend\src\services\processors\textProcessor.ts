import fs from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  DocumentMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk,
  ChunkingStrategy
} from '@/types/document';
import { TextChunkingService } from '@/services/textChunkingService';
import { TextPreprocessingService } from '@/services/textPreprocessingService';
import { MetadataExtractionService } from '@/services/metadataExtractionService';

export class TextProcessor implements IDocumentProcessor {
  private chunkingService: TextChunkingService;
  private preprocessingService: TextPreprocessingService;
  private metadataService: MetadataExtractionService;

  constructor() {
    this.chunkingService = TextChunkingService.getInstance();
    this.preprocessingService = TextPreprocessingService.getInstance();
    this.metadataService = MetadataExtractionService.getInstance();
  }

  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'text/plain' ||
      mimeType.startsWith('text/') ||
      mimeType === 'application/octet-stream'; // Fallback for unknown text files
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.TEXT;
  }

  /**
   * Process a text file and extract content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing text file: ${filePath}`);

      // Detect encoding and read the file
      const { text, encoding } = await this.readTextFile(filePath);

      // Extract metadata
      const metadata = await this.extractMetadata(filePath, text, encoding);

      // Create chunks if requested
      const chunks = options?.chunkSize ?
        this.createChunksWithUnifiedService(text, options, metadata) :
        undefined;

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;
      metadata.encoding = encoding;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`Text processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown text processing error';
      logger.error(`Text processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Read text file with encoding detection
   */
  private async readTextFile(filePath: string): Promise<{ text: string; encoding: string }> {
    try {
      // Try UTF-8 first
      const text = fs.readFileSync(filePath, 'utf-8');

      // Check if the text contains replacement characters (indicating encoding issues)
      if (text.includes('\uFFFD')) {
        // Try other encodings
        const encodings = ['latin1', 'ascii'];

        for (const encoding of encodings) {
          try {
            const alternativeText = fs.readFileSync(filePath, encoding as BufferEncoding);
            if (!alternativeText.includes('\uFFFD')) {
              return { text: alternativeText, encoding };
            }
          } catch (error) {
            // Continue to next encoding
          }
        }
      }

      return { text, encoding: 'utf-8' };
    } catch (error) {
      throw new Error(`Failed to read text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract metadata from text file
   */
  private async extractMetadata(filePath: string, text: string, encoding: string): Promise<DocumentMetadata> {
    try {
      const stats = fs.statSync(filePath);

      // Analyze text content
      const lineCount = text.split('\n').length;
      const language = this.detectLanguage(text);

      const metadata: DocumentMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'text/plain',
        size: stats.size,
        type: DocumentType.TEXT,
        encoding,
        language,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        // Additional text-specific metadata
        lines: lineCount
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract text metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Simple language detection based on common words
   */
  private detectLanguage(text: string): string {
    const sample = text.toLowerCase().substring(0, 1000); // Use first 1000 chars

    const languagePatterns = {
      'en': ['the', 'and', 'that', 'have', 'for', 'not', 'with', 'you', 'this', 'but'],
      'es': ['que', 'de', 'no', 'la', 'el', 'en', 'y', 'a', 'es', 'se'],
      'fr': ['que', 'de', 'et', 'le', 'la', 'les', 'des', 'en', 'un', 'du'],
      'de': ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich'],
      'it': ['che', 'di', 'la', 'il', 'e', 'le', 'un', 'in', 'per', 'non']
    };

    let bestLanguage = 'unknown';
    let maxMatches = 0;

    Object.entries(languagePatterns).forEach(([lang, words]) => {
      const matches = words.filter(word =>
        sample.includes(` ${word} `) ||
        sample.startsWith(`${word} `) ||
        sample.endsWith(` ${word}`)
      ).length;

      if (matches > maxMatches) {
        maxMatches = matches;
        bestLanguage = lang;
      }
    });

    return maxMatches > 2 ? bestLanguage : 'unknown';
  }

  /**
   * Create text chunks using the unified chunking service
   */
  private createChunksWithUnifiedService(
    text: string,
    options: ProcessingOptions,
    sourceMetadata: DocumentMetadata
  ): TextChunk[] {
    // Create document-specific chunking configuration
    const chunkingConfig = this.chunkingService.createDocumentSpecificConfig('text', {
      chunkSize: options.chunkSize || 1000,
      chunkOverlap: options.chunkOverlap || 200,
      strategy: ChunkingStrategy.PARAGRAPH, // Use paragraph-aware chunking for text files
      preserveFormatting: options.preserveFormatting !== false,
      respectWordBoundaries: true
    });

    // Preprocessing options
    const preprocessingOptions = {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    };

    // Create source metadata for chunks
    const chunkSourceMetadata = {
      source: 'text',
      documentType: 'text',
      fileName: sourceMetadata.fileName,
      filePath: sourceMetadata.filePath,
      mimeType: sourceMetadata.mimeType,
      encoding: sourceMetadata.encoding || 'utf-8'
    };

    // Generate chunks using the unified service
    const chunks = this.chunkingService.chunkText(
      text,
      chunkingConfig,
      preprocessingOptions,
      chunkSourceMetadata
    );

    // Extract enhanced metadata for each chunk
    return chunks.map(chunk =>
      this.metadataService.extractChunkMetadata(chunk, chunkSourceMetadata, text)
    );
  }

  /**
   * Legacy create chunks method (deprecated - kept for backward compatibility)
   */
  private createChunks(text: string, chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];

    // Try to split by paragraphs first
    const paragraphs = text.split(/\n\s*\n/);

    if (paragraphs.length > 1 && paragraphs.some(p => p.length < chunkSize)) {
      // Create chunks by paragraphs
      let currentChunk = '';
      let currentStart = 0;

      paragraphs.forEach((paragraph, index) => {
        const trimmedParagraph = paragraph.trim();

        if (currentChunk.length + trimmedParagraph.length > chunkSize && currentChunk.length > 0) {
          // Save current chunk
          chunks.push({
            id: `text_paragraph_chunk_${chunks.length}`,
            text: currentChunk.trim(),
            startIndex: currentStart,
            endIndex: currentStart + currentChunk.length,
            metadata: {
              source: 'text',
              chunkIndex: chunks.length,
              type: 'paragraph'
            }
          });

          // Start new chunk with overlap
          const overlapText = currentChunk.substring(Math.max(0, currentChunk.length - overlap));
          currentChunk = overlapText + '\n\n' + trimmedParagraph;
          currentStart += currentChunk.length - overlapText.length - trimmedParagraph.length - 2;
        } else {
          if (currentChunk.length > 0) {
            currentChunk += '\n\n';
          }
          currentChunk += trimmedParagraph;
        }

        // If this is the last paragraph, save the chunk
        if (index === paragraphs.length - 1 && currentChunk.trim().length > 0) {
          chunks.push({
            id: `text_paragraph_chunk_${chunks.length}`,
            text: currentChunk.trim(),
            startIndex: currentStart,
            endIndex: currentStart + currentChunk.length,
            metadata: {
              source: 'text',
              chunkIndex: chunks.length,
              type: 'paragraph'
            }
          });
        }
      });
    } else {
      // Fall back to character-based chunking
      let currentPos = 0;

      while (currentPos < text.length) {
        const endPos = Math.min(currentPos + chunkSize, text.length);
        const chunkText = text.substring(currentPos, endPos);

        chunks.push({
          id: `text_chunk_${chunks.length}`,
          text: chunkText,
          startIndex: currentPos,
          endIndex: endPos,
          metadata: {
            source: 'text',
            chunkIndex: chunks.length,
            type: 'character'
          }
        });

        // Move to next chunk with overlap
        currentPos = endPos - overlap;
        if (currentPos >= text.length) break;
      }
    }

    return chunks;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): DocumentMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'text/plain',
      size: stats.size,
      type: DocumentType.TEXT,
      encoding: 'utf-8',
      language: 'unknown',
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): DocumentMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
