/**
 * Tests for Embedding Routes
 */

import request from 'supertest';
import express from 'express';
import { embeddingsRoutes } from '../routes/embeddings';
import { getEmbeddingService } from '../services/embeddingService';

// Mock the config first
jest.mock('../config/environment', () => ({
  config: {
    env: 'test',
    port: 8001,
    apiPrefix: '/v1',
    database: {
      uri: 'mongodb://localhost:27017/verbyte_test',
      testUri: 'mongodb://localhost:27017/verbyte_test'
    },
    qdrant: {
      url: 'http://localhost:6333',
      apiKey: '',
      collectionName: 'verbyte_documents_test'
    },
    auth: {
      jwtSecret: 'test-secret',
      jwtExpiresIn: '7d',
      bcryptRounds: 12
    },
    upload: {
      maxFileSize: 10485760,
      uploadDir: 'uploads',
      allowedFileTypes: ['pdf', 'docx', 'txt']
    },
    embedding: {
      service: 'huggingface',
      model: 'sentence-transformers/all-MiniLM-L6-v2',
      dimension: 384
    },
    huggingface: {
      apiKey: 'test-hf-key',
      apiUrl: 'https://api-inference.huggingface.co/pipeline/feature-extraction'
    },
    openai: {
      apiKey: 'test-openai-key',
      embeddingModel: 'text-embedding-ada-002',
      chatModel: 'gpt-3.5-turbo'
    },
    ollama: {
      baseUrl: 'http://localhost:11434',
      embeddingModel: 'nomic-embed-text',
      chatModel: 'llama3.1'
    },
    llm: {
      service: 'openai',
      maxTokens: 512,
      temperature: 0.1,
      contextWindow: 3900
    },
    rag: {
      similarityTopK: 5,
      similarityThreshold: 0.7,
      chunkSize: 512,
      chunkOverlap: 50,
      rerankEnabled: false,
      rerankModel: 'cross-encoder/ms-marco-MiniLM-L-2-v2',
      rerankTopN: 3
    },
    rateLimiting: {
      windowMs: 900000,
      maxRequests: 100
    }
  }
}));

// Mock the embedding service
jest.mock('../services/embeddingService');

// Mock the logger
jest.mock('../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('Embedding Routes', () => {
  let app: express.Application;
  let mockEmbeddingService: jest.Mocked<any>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock embedding service
    mockEmbeddingService = {
      createEmbeddingResponse: jest.fn()
    };

    (getEmbeddingService as jest.Mock).mockReturnValue(mockEmbeddingService);

    // Setup Express app
    app = express();
    app.use(express.json());
    app.use('/v1/embeddings', embeddingsRoutes);
  });

  describe('POST /v1/embeddings', () => {
    it('should generate embeddings for string input', async () => {
      const mockResponse = {
        object: 'list',
        model: 'test-model',
        data: [{
          index: 0,
          object: 'embedding',
          embedding: [0.1, 0.2, 0.3]
        }],
        usage: {
          prompt_tokens: 3,
          total_tokens: 3
        }
      };

      mockEmbeddingService.createEmbeddingResponse.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: 'Hello world' })
        .expect(200);

      expect(response.body).toEqual(mockResponse);
      expect(mockEmbeddingService.createEmbeddingResponse).toHaveBeenCalledWith('Hello world');
    });

    it('should generate embeddings for array input', async () => {
      const mockResponse = {
        object: 'list',
        model: 'test-model',
        data: [
          {
            index: 0,
            object: 'embedding',
            embedding: [0.1, 0.2, 0.3]
          },
          {
            index: 1,
            object: 'embedding',
            embedding: [0.4, 0.5, 0.6]
          }
        ],
        usage: {
          prompt_tokens: 6,
          total_tokens: 6
        }
      };

      mockEmbeddingService.createEmbeddingResponse.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: ['Hello', 'world'] })
        .expect(200);

      expect(response.body).toEqual(mockResponse);
      expect(mockEmbeddingService.createEmbeddingResponse).toHaveBeenCalledWith(['Hello', 'world']);
    });

    it('should accept optional model parameter', async () => {
      const mockResponse = {
        object: 'list',
        model: 'custom-model',
        data: [{
          index: 0,
          object: 'embedding',
          embedding: [0.1, 0.2, 0.3]
        }],
        usage: {
          prompt_tokens: 3,
          total_tokens: 3
        }
      };

      mockEmbeddingService.createEmbeddingResponse.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/v1/embeddings')
        .send({
          input: 'Hello world',
          model: 'custom-model'
        })
        .expect(200);

      expect(response.body).toEqual(mockResponse);
    });

    it('should return 400 for missing input', async () => {
      const response = await request(app)
        .post('/v1/embeddings')
        .send({})
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Invalid request parameters',
        code: 'VALIDATION_ERROR'
      });
      expect(response.body.errors).toBeDefined();
    });

    it('should return 400 for empty string input', async () => {
      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: '' })
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Invalid request parameters',
        code: 'VALIDATION_ERROR'
      });
    });

    it('should return 400 for empty array input', async () => {
      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: [] })
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Invalid request parameters',
        code: 'VALIDATION_ERROR'
      });
    });

    it('should return 400 for array with empty strings', async () => {
      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: ['Hello', '', 'world'] })
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Invalid request parameters',
        code: 'VALIDATION_ERROR'
      });
    });

    it('should return 400 for invalid input type', async () => {
      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: 123 })
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Invalid request parameters',
        code: 'VALIDATION_ERROR'
      });
    });

    it('should return 400 for invalid model type', async () => {
      const response = await request(app)
        .post('/v1/embeddings')
        .send({
          input: 'Hello world',
          model: 123
        })
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Invalid request parameters',
        code: 'VALIDATION_ERROR'
      });
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Service unavailable');
      mockEmbeddingService.createEmbeddingResponse.mockRejectedValue(error);

      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: 'Hello world' })
        .expect(500);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Failed to generate embeddings',
        details: 'Service unavailable',
        code: 'EMBEDDING_GENERATION_ERROR'
      });
    });

    it('should handle unknown errors gracefully', async () => {
      mockEmbeddingService.createEmbeddingResponse.mockRejectedValue('Unknown error');

      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: 'Hello world' })
        .expect(500);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Failed to generate embeddings',
        details: 'Unknown error occurred',
        code: 'EMBEDDING_GENERATION_ERROR'
      });
    });

    it('should handle large input arrays', async () => {
      const largeInput = Array(100).fill('Test text');
      const mockResponse = {
        object: 'list',
        model: 'test-model',
        data: largeInput.map((_, index) => ({
          index,
          object: 'embedding',
          embedding: [0.1, 0.2, 0.3]
        })),
        usage: {
          prompt_tokens: 300,
          total_tokens: 300
        }
      };

      mockEmbeddingService.createEmbeddingResponse.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: largeInput })
        .expect(200);

      expect(response.body.data).toHaveLength(100);
      expect(mockEmbeddingService.createEmbeddingResponse).toHaveBeenCalledWith(largeInput);
    });

    it('should trim whitespace from input strings', async () => {
      const mockResponse = {
        object: 'list',
        model: 'test-model',
        data: [{
          index: 0,
          object: 'embedding',
          embedding: [0.1, 0.2, 0.3]
        }],
        usage: {
          prompt_tokens: 3,
          total_tokens: 3
        }
      };

      mockEmbeddingService.createEmbeddingResponse.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/v1/embeddings')
        .send({ input: '  Hello world  ' })
        .expect(200);

      expect(response.body).toEqual(mockResponse);
    });
  });
});
