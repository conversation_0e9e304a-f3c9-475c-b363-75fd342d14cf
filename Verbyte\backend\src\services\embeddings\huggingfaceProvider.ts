/**
 * HuggingFace Embedding Provider
 * Uses HuggingFace Inference API for generating embeddings
 */

import { HfInference } from '@huggingface/inference';
import { config } from '../../config/environment';
import { logger } from '../../utils/logger';
import { EmbeddingProvider } from '../../types/document';

export class HuggingFaceEmbeddingProvider implements EmbeddingProvider {
  private hf: HfInference;
  private readonly model: string;
  private readonly dimension: number;
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 1000; // 1 second

  constructor() {
    if (!config.huggingface.apiKey) {
      throw new Error('HuggingFace API key is required but not provided');
    }

    this.hf = new HfInference(config.huggingface.apiKey);
    this.model = config.embedding.model;
    this.dimension = config.embedding.dimension;

    logger.info(`Initialized HuggingFace embedding provider with model: ${this.model}`);
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (!texts || texts.length === 0) {
      throw new Error('No texts provided for embedding generation');
    }

    logger.debug(`Generating HuggingFace embeddings for ${texts.length} texts`);

    try {
      // Process texts in batches to avoid API limits
      const batchSize = 10; // HuggingFace API typically handles small batches well
      const results: number[][] = [];

      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchResults = await this.processBatch(batch);
        results.push(...batchResults);
      }

      logger.debug(`Successfully generated ${results.length} HuggingFace embeddings`);
      return results;

    } catch (error) {
      logger.error('Error generating HuggingFace embeddings:', error);
      throw new Error(`HuggingFace embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process a batch of texts
   */
  private async processBatch(texts: string[]): Promise<number[][]> {
    const results: number[][] = [];

    for (const text of texts) {
      const embedding = await this.generateSingleEmbedding(text);
      results.push(embedding);
    }

    return results;
  }

  /**
   * Generate embedding for a single text with retry logic
   */
  private async generateSingleEmbedding(text: string): Promise<number[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        logger.debug(`HuggingFace embedding attempt ${attempt} for text length: ${text.length}`);

        const response = await this.hf.featureExtraction({
          model: this.model,
          inputs: text
        });

        // Handle different response formats
        let embedding: number[];
        
        if (Array.isArray(response)) {
          if (Array.isArray(response[0])) {
            // 2D array - take the first row
            embedding = response[0] as number[];
          } else {
            // 1D array
            embedding = response as number[];
          }
        } else {
          throw new Error('Unexpected response format from HuggingFace API');
        }

        // Validate embedding dimension
        if (embedding.length !== this.dimension) {
          logger.warn(`Expected embedding dimension ${this.dimension}, got ${embedding.length}. Updating configuration.`);
          // Update the dimension in memory (not persisted)
          (this as any).dimension = embedding.length;
        }

        return embedding;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        logger.warn(`HuggingFace embedding attempt ${attempt} failed:`, lastError.message);

        if (attempt < this.maxRetries) {
          // Exponential backoff
          const delay = this.retryDelay * Math.pow(2, attempt - 1);
          await this.sleep(delay);
        }
      }
    }

    throw new Error(`Failed to generate HuggingFace embedding after ${this.maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * Get the embedding dimension
   */
  getDimension(): number {
    return this.dimension;
  }

  /**
   * Get the model name
   */
  getModelName(): string {
    return this.model;
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate API key and model availability
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      logger.debug('Validating HuggingFace configuration...');
      
      // Test with a simple text
      const testText = "This is a test sentence for validation.";
      await this.generateSingleEmbedding(testText);
      
      logger.info('HuggingFace configuration validated successfully');
      return true;

    } catch (error) {
      logger.error('HuggingFace configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Get model information from HuggingFace
   */
  async getModelInfo(): Promise<any> {
    try {
      // Note: HuggingFace Inference API doesn't provide a direct model info endpoint
      // This is a placeholder for future implementation
      return {
        model: this.model,
        dimension: this.dimension,
        provider: 'huggingface',
        status: 'available'
      };
    } catch (error) {
      logger.error('Error getting HuggingFace model info:', error);
      throw error;
    }
  }

  /**
   * Check if the model supports batch processing
   */
  supportsBatchProcessing(): boolean {
    return true;
  }

  /**
   * Get recommended batch size for this provider
   */
  getRecommendedBatchSize(): number {
    return 10;
  }

  /**
   * Estimate cost for embedding generation (if applicable)
   */
  estimateCost(textCount: number): { currency: string; amount: number } {
    // HuggingFace Inference API is free for many models
    // This is a placeholder for future cost estimation
    return {
      currency: 'USD',
      amount: 0
    };
  }

  /**
   * Get provider-specific metrics
   */
  getMetrics(): { 
    provider: string; 
    model: string; 
    dimension: number; 
    maxRetries: number;
    retryDelay: number;
  } {
    return {
      provider: 'huggingface',
      model: this.model,
      dimension: this.dimension,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}
