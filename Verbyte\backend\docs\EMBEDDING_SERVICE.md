# Embedding Service Documentation

## Overview

The Verbyte Embedding Service provides a comprehensive solution for generating text embeddings using multiple providers including HuggingFace, OpenAI, and Ollama. This service maintains exact functional parity with the Python PrivateGPT embedding system while offering enhanced performance through caching and batch processing.

## Features

- **Multi-Provider Support**: HuggingFace Inference API, OpenAI Embeddings API, and Ollama local models
- **Intelligent Caching**: Memory-based caching with TTL and automatic cleanup
- **Batch Processing**: Optimized batch processing for improved performance
- **Error Handling**: Comprehensive error handling with retry logic
- **API Compatibility**: Exact API compatibility with Python PrivateGPT
- **TypeScript Support**: Full TypeScript support with comprehensive type definitions

## Architecture

```
EmbeddingService (Main Service)
├── EmbeddingProvider (Interface)
│   ├── HuggingFaceEmbeddingProvider
│   ├── OpenAIEmbeddingProvider
│   └── OllamaEmbeddingProvider
├── EmbeddingCache (Caching Layer)
└── API Routes (/v1/embeddings)
```

## Configuration

### Environment Variables

```bash
# Embedding Service Configuration
EMBEDDING_SERVICE=huggingface  # huggingface | openai | ollama
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384

# HuggingFace Configuration
HUGGINGFACE_API_KEY=your_hf_token
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/pipeline/feature-extraction

# OpenAI Configuration
OPENAI_API_KEY=your_openai_key
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
```

## Usage

### Basic Usage

```typescript
import { getEmbeddingService } from './services/embeddingService';

const embeddingService = getEmbeddingService();

// Generate embedding for single text
const embedding = await embeddingService.textEmbedding('Hello world');
console.log(embedding.embedding); // [0.1, 0.2, 0.3, ...]

// Generate embeddings for multiple texts
const embeddings = await embeddingService.textsEmbeddings([
  'First text',
  'Second text',
  'Third text'
]);
```

### API Usage

```bash
# Generate embeddings via REST API
curl -X POST http://localhost:8001/v1/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "input": "Text to embed"
  }'

# Multiple texts
curl -X POST http://localhost:8001/v1/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "input": ["First text", "Second text", "Third text"]
  }'
```

### Response Format

```json
{
  "object": "list",
  "model": "sentence-transformers/all-MiniLM-L6-v2",
  "data": [
    {
      "index": 0,
      "object": "embedding",
      "embedding": [0.1, 0.2, 0.3, ...]
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "total_tokens": 10
  }
}
```

## Providers

### HuggingFace Provider

- **Models**: Any HuggingFace embedding model
- **API**: HuggingFace Inference API
- **Batch Size**: 10 texts per batch
- **Retry Logic**: 3 attempts with exponential backoff
- **Cost**: Free for many models

### OpenAI Provider

- **Models**: text-embedding-ada-002, text-embedding-3-small, text-embedding-3-large
- **API**: OpenAI Embeddings API
- **Batch Size**: Up to 2048 texts per batch
- **Retry Logic**: 3 attempts with rate limit handling
- **Cost**: Varies by model (see OpenAI pricing)

### Ollama Provider

- **Models**: Any Ollama-compatible embedding model
- **API**: Local Ollama server
- **Batch Size**: 1 text at a time
- **Auto-pull**: Automatically downloads models if not available
- **Cost**: Free (local processing)

## Caching

The embedding service includes an intelligent caching system:

- **Memory Cache**: In-memory LRU cache with configurable size
- **TTL**: 24-hour default time-to-live
- **Automatic Cleanup**: Periodic cleanup of expired entries
- **Cache Key**: SHA-256 hash of text + model combination
- **Performance**: Significant speedup for repeated texts

### Cache Configuration

```typescript
// Default cache settings
const cache = new EmbeddingCache(
  10000,  // Max 10,000 entries
  24      // 24-hour TTL
);
```

## Batch Processing

Optimized batch processing for improved performance:

```typescript
// Process large batches efficiently
const embeddings = await embeddingService.batchEmbeddings(
  largeTextArray,
  10  // Batch size
);
```

## Error Handling

Comprehensive error handling with specific error types:

- **Validation Errors**: Invalid input parameters
- **Provider Errors**: API failures, rate limits, authentication
- **Network Errors**: Connection timeouts, network failures
- **Configuration Errors**: Missing API keys, invalid models

## Testing

Run the embedding tests:

```bash
# Run all embedding tests
npm test -- --testPathPattern=embedding

# Run specific test files
npm test embeddingService.test.ts
npm test embeddingRoutes.test.ts
```

## Performance Considerations

1. **Caching**: Enable caching for frequently used texts
2. **Batch Processing**: Use batch processing for multiple texts
3. **Provider Selection**: Choose appropriate provider based on needs:
   - HuggingFace: Free, good for development
   - OpenAI: High quality, paid service
   - Ollama: Local processing, privacy-focused

## Monitoring

Monitor embedding service performance:

```typescript
// Get cache statistics
const stats = await embeddingService.getCacheStats();
console.log(`Cache hit rate: ${stats.hits / (stats.hits + stats.misses) * 100}%`);

// Get provider metrics
const metrics = embeddingService.getProvider().getMetrics();
console.log('Provider metrics:', metrics);
```

## Migration from Python

The TypeScript implementation maintains exact API compatibility:

```python
# Python (original)
embeddings = service.texts_embeddings(texts)

# TypeScript (new)
const embeddings = await service.textsEmbeddings(texts);
```

## Troubleshooting

### Common Issues

1. **API Key Missing**: Ensure environment variables are set
2. **Model Not Found**: Verify model name and availability
3. **Rate Limits**: Implement proper retry logic and delays
4. **Memory Issues**: Monitor cache size and adjust limits

### Debug Mode

Enable debug logging:

```bash
NODE_ENV=development npm start
```

## Contributing

When adding new embedding providers:

1. Implement the `EmbeddingProvider` interface
2. Add provider to the service factory
3. Update configuration validation
4. Add comprehensive tests
5. Update documentation

## License

This embedding service is part of the Verbyte project and follows the same license terms.
