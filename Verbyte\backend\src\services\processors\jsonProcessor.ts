import fs from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  JSONMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class JSONProcessor implements IDocumentProcessor {
  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'application/json' ||
           mimeType === 'text/json' ||
           mimeType.includes('json');
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.JSON;
  }

  /**
   * Process a JSON file and extract text content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing JSON file: ${filePath}`);

      // Read the JSON file
      const jsonContent = fs.readFileSync(filePath, 'utf-8');

      // Parse JSON
      const jsonData = JSON.parse(jsonContent);

      // Extract metadata
      const metadata = await this.extractMetadata(filePath, jsonData, jsonContent);

      // Convert JSON to readable text
      const text = this.jsonToText(jsonData, options?.preserveFormatting);

      // Create chunks if requested
      const chunks = options?.chunkSize ? 
        this.createChunks(text, jsonData, options.chunkSize, options.chunkOverlap || 0) : 
        undefined;

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`JSON processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown JSON processing error';
      logger.error(`JSON processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Convert JSON data to readable text
   */
  private jsonToText(data: any, preserveFormatting?: boolean): string {
    if (preserveFormatting) {
      return JSON.stringify(data, null, 2);
    }

    return this.extractTextFromValue(data, '');
  }

  /**
   * Recursively extract text from JSON values
   */
  private extractTextFromValue(value: any, path: string): string {
    const textParts: string[] = [];

    if (value === null || value === undefined) {
      return '';
    }

    if (typeof value === 'string') {
      textParts.push(value);
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      textParts.push(String(value));
    } else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        const itemPath = path ? `${path}[${index}]` : `[${index}]`;
        const itemText = this.extractTextFromValue(item, itemPath);
        if (itemText) {
          textParts.push(itemText);
        }
      });
    } else if (typeof value === 'object') {
      Object.entries(value).forEach(([key, val]) => {
        const keyPath = path ? `${path}.${key}` : key;
        
        // Add key as context if it's meaningful
        if (this.isMeaningfulKey(key)) {
          textParts.push(`${key}:`);
        }
        
        const valText = this.extractTextFromValue(val, keyPath);
        if (valText) {
          textParts.push(valText);
        }
      });
    }

    return textParts.join(' ').trim();
  }

  /**
   * Check if a key provides meaningful context
   */
  private isMeaningfulKey(key: string): boolean {
    const meaningfulKeys = [
      'title', 'name', 'description', 'content', 'text', 'message',
      'summary', 'body', 'comment', 'note', 'label', 'caption'
    ];
    
    return meaningfulKeys.some(meaningful => 
      key.toLowerCase().includes(meaningful)
    );
  }

  /**
   * Extract metadata from JSON data
   */
  private async extractMetadata(filePath: string, jsonData: any, jsonContent: string): Promise<JSONMetadata> {
    try {
      const stats = fs.statSync(filePath);

      // Analyze JSON structure
      const structure = Array.isArray(jsonData) ? 'array' : 
                       typeof jsonData === 'object' ? 'object' : 'mixed';
      
      const analysis = this.analyzeStructure(jsonData);

      const metadata: JSONMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'application/json',
        size: stats.size,
        type: DocumentType.JSON,
        structure,
        depth: analysis.depth,
        keyCount: analysis.keyCount,
        hasNestedObjects: analysis.hasNestedObjects,
        hasArrays: analysis.hasArrays,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract JSON metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Analyze JSON structure
   */
  private analyzeStructure(data: any, currentDepth: number = 0): {
    depth: number;
    keyCount: number;
    hasNestedObjects: boolean;
    hasArrays: boolean;
  } {
    let maxDepth = currentDepth;
    let keyCount = 0;
    let hasNestedObjects = false;
    let hasArrays = false;

    if (Array.isArray(data)) {
      hasArrays = true;
      data.forEach(item => {
        const analysis = this.analyzeStructure(item, currentDepth + 1);
        maxDepth = Math.max(maxDepth, analysis.depth);
        keyCount += analysis.keyCount;
        hasNestedObjects = hasNestedObjects || analysis.hasNestedObjects;
        hasArrays = hasArrays || analysis.hasArrays;
      });
    } else if (typeof data === 'object' && data !== null) {
      if (currentDepth > 0) {
        hasNestedObjects = true;
      }
      
      Object.entries(data).forEach(([key, value]) => {
        keyCount++;
        const analysis = this.analyzeStructure(value, currentDepth + 1);
        maxDepth = Math.max(maxDepth, analysis.depth);
        keyCount += analysis.keyCount;
        hasNestedObjects = hasNestedObjects || analysis.hasNestedObjects;
        hasArrays = hasArrays || analysis.hasArrays;
      });
    }

    return {
      depth: maxDepth,
      keyCount,
      hasNestedObjects,
      hasArrays
    };
  }

  /**
   * Create text chunks from JSON content
   */
  private createChunks(text: string, jsonData: any, chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];

    if (Array.isArray(jsonData)) {
      // Chunk by array items
      const itemsPerChunk = Math.max(1, Math.floor(chunkSize / 200)); // Estimate items per chunk
      
      for (let i = 0; i < jsonData.length; i += itemsPerChunk) {
        const endIndex = Math.min(i + itemsPerChunk, jsonData.length);
        const chunkItems = jsonData.slice(i, endIndex);
        const chunkText = this.jsonToText(chunkItems);

        chunks.push({
          id: `json_array_chunk_${chunks.length}`,
          text: chunkText,
          startIndex: i,
          endIndex: endIndex,
          metadata: {
            source: 'json',
            chunkIndex: chunks.length,
            arrayStartIndex: i,
            arrayEndIndex: endIndex - 1,
            itemCount: chunkItems.length
          }
        });
      }
    } else {
      // Chunk by text length
      let currentPos = 0;

      while (currentPos < text.length) {
        const endPos = Math.min(currentPos + chunkSize, text.length);
        const chunkText = text.substring(currentPos, endPos);

        chunks.push({
          id: `json_chunk_${chunks.length}`,
          text: chunkText,
          startIndex: currentPos,
          endIndex: endPos,
          metadata: {
            source: 'json',
            chunkIndex: chunks.length
          }
        });

        // Move to next chunk with overlap
        currentPos = endPos - overlap;
        if (currentPos >= text.length) break;
      }
    }

    return chunks;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): JSONMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'application/json',
      size: stats.size,
      type: DocumentType.JSON,
      structure: 'mixed',
      depth: 0,
      keyCount: 0,
      hasNestedObjects: false,
      hasArrays: false,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): JSONMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
