import { logger } from '@/utils/logger';
import { UploadService } from './uploadService';
import { DocumentProcessingService } from './documentProcessor';
import { IUpload, UploadStatus, ProcessingStatus } from '@/models/Upload';
import { TextExtractionResult, ProcessingOptions } from '@/types/document';

/**
 * Service that integrates document processing with the upload system
 */
export class IntegratedDocumentProcessingService {
  private uploadService: UploadService;
  private documentProcessor: DocumentProcessingService;

  constructor() {
    this.uploadService = new UploadService();
    this.documentProcessor = new DocumentProcessingService();
  }

  /**
   * Process a document that has been uploaded
   */
  async processUploadedDocument(
    uploadId: string,
    options?: ProcessingOptions
  ): Promise<TextExtractionResult> {
    try {
      logger.info(`Starting document processing for upload: ${uploadId}`);

      // Get upload record
      const upload = await this.uploadService.getUploadById(uploadId);
      if (!upload) {
        throw new Error(`Upload not found: ${uploadId}`);
      }

      // Update status to processing
      await this.uploadService.updateStatus(
        uploadId,
        UploadStatus.PROCESSING,
        ProcessingStatus.EXTRACTING_TEXT
      );

      // Update progress
      await this.uploadService.updateProgress(uploadId, 100, 10);

      // Process the document
      const result = await this.documentProcessor.processDocument(upload.path, options);

      if (result.success) {
        // Update upload metadata with processing results
        await this.updateUploadMetadata(upload, result);

        // Update status to completed
        await this.uploadService.updateStatus(
          uploadId,
          UploadStatus.COMPLETED,
          ProcessingStatus.COMPLETED
        );

        // Update progress to 100%
        await this.uploadService.updateProgress(uploadId, 100, 100);

        logger.info(`Document processing completed successfully for upload: ${uploadId}`);
      } else {
        // Handle processing failure
        await this.uploadService.setError(
          uploadId,
          result.error || 'Document processing failed',
          'PROCESSING_FAILED'
        );

        logger.error(`Document processing failed for upload: ${uploadId}`, result.error);
      }

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown processing error';
      
      // Update upload with error
      await this.uploadService.setError(uploadId, errorMessage, 'PROCESSING_ERROR');

      logger.error(`Document processing error for upload: ${uploadId}`, error);
      throw error;
    }
  }

  /**
   * Process multiple uploaded documents in batch
   */
  async processBatchUploads(
    uploadIds: string[],
    options?: ProcessingOptions
  ): Promise<{ uploadId: string; result: TextExtractionResult }[]> {
    logger.info(`Starting batch processing for ${uploadIds.length} uploads`);

    const results: { uploadId: string; result: TextExtractionResult }[] = [];

    for (const uploadId of uploadIds) {
      try {
        const result = await this.processUploadedDocument(uploadId, options);
        results.push({ uploadId, result });
      } catch (error) {
        logger.error(`Failed to process upload ${uploadId} in batch:`, error);
        
        // Create error result
        results.push({
          uploadId,
          result: {
            text: '',
            metadata: {
              filename: 'unknown',
              originalName: 'unknown',
              mimeType: 'unknown',
              size: 0,
              type: 'text' as any,
              error: error instanceof Error ? error.message : 'Unknown error'
            },
            processingTime: 0,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });
      }
    }

    logger.info(`Batch processing completed: ${results.filter(r => r.result.success).length}/${uploadIds.length} successful`);
    return results;
  }

  /**
   * Process pending uploads automatically
   */
  async processPendingUploads(options?: ProcessingOptions): Promise<number> {
    try {
      const pendingUploads = await this.uploadService.getPendingUploads();
      
      if (pendingUploads.length === 0) {
        logger.debug('No pending uploads to process');
        return 0;
      }

      logger.info(`Found ${pendingUploads.length} pending uploads to process`);

      const uploadIds = pendingUploads.map(upload => upload._id.toString());
      await this.processBatchUploads(uploadIds, options);

      return pendingUploads.length;
    } catch (error) {
      logger.error('Failed to process pending uploads:', error);
      throw error;
    }
  }

  /**
   * Check if a file type is supported for processing
   */
  isFileSupported(filePath: string): boolean {
    return this.documentProcessor.isSupported(filePath);
  }

  /**
   * Get supported file extensions
   */
  getSupportedExtensions(): string[] {
    return this.documentProcessor.getSupportedExtensions();
  }

  /**
   * Update upload metadata with processing results
   */
  private async updateUploadMetadata(
    upload: IUpload,
    result: TextExtractionResult
  ): Promise<void> {
    try {
      // Update the upload document with extracted metadata
      upload.metadata = {
        ...upload.metadata,
        extractedText: true,
        pages: result.metadata.pages,
        wordCount: result.metadata.wordCount,
        characterCount: result.metadata.characterCount,
        language: result.metadata.language,
        documentType: result.metadata.type,
        processingTime: result.processingTime,
        chunks: result.chunks?.length || 0
      };

      await upload.save();
      logger.debug(`Updated metadata for upload: ${upload._id}`);
    } catch (error) {
      logger.error(`Failed to update upload metadata: ${upload._id}`, error);
      // Don't throw here as the processing was successful
    }
  }

  /**
   * Get processing statistics
   */
  async getProcessingStats(): Promise<{
    totalProcessed: number;
    successfulProcessing: number;
    failedProcessing: number;
    pendingProcessing: number;
    averageProcessingTime: number;
  }> {
    try {
      const stats = await this.uploadService.getUploadStats();
      
      const totalProcessed = stats.byProcessingStatus.completed + stats.byProcessingStatus.failed;
      const successfulProcessing = stats.byProcessingStatus.completed;
      const failedProcessing = stats.byProcessingStatus.failed;
      const pendingProcessing = stats.byProcessingStatus.pending;

      // Calculate average processing time (this would need to be tracked in metadata)
      const averageProcessingTime = 0; // TODO: Implement based on stored processing times

      return {
        totalProcessed,
        successfulProcessing,
        failedProcessing,
        pendingProcessing,
        averageProcessingTime
      };
    } catch (error) {
      logger.error('Failed to get processing statistics:', error);
      throw error;
    }
  }

  /**
   * Reprocess a failed upload
   */
  async reprocessUpload(uploadId: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    try {
      logger.info(`Reprocessing upload: ${uploadId}`);

      // Reset upload status
      await this.uploadService.updateStatus(
        uploadId,
        UploadStatus.UPLOADED,
        ProcessingStatus.PENDING
      );

      // Reset progress
      await this.uploadService.updateProgress(uploadId, 100, 0);

      // Process the document
      return await this.processUploadedDocument(uploadId, options);
    } catch (error) {
      logger.error(`Failed to reprocess upload: ${uploadId}`, error);
      throw error;
    }
  }

  /**
   * Get document processor configuration
   */
  getProcessorConfig() {
    return this.documentProcessor.getConfig();
  }

  /**
   * Update document processor configuration
   */
  updateProcessorConfig(config: any) {
    this.documentProcessor.updateConfig(config);
  }
}
