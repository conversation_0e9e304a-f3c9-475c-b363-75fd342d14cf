/**
 * Ollama Embedding Provider
 * Uses Ollama API for generating embeddings with local models
 */

import { Ollama } from 'ollama';
import { config } from '../../config/environment';
import { logger } from '../../utils/logger';
import { EmbeddingProvider } from '../../types/document';

export class OllamaEmbeddingProvider implements EmbeddingProvider {
  private ollama: Ollama;
  private readonly model: string;
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 2000; // 2 seconds (local models may need more time)
  private readonly connectionTimeout: number = 30000; // 30 seconds
  private modelDimension: number | null = null;

  constructor() {
    this.ollama = new Ollama({
      host: config.ollama.baseUrl,
    });

    this.model = config.ollama.embeddingModel;

    logger.info(`Initialized Ollama embedding provider with model: ${this.model} at ${config.ollama.baseUrl}`);
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (!texts || texts.length === 0) {
      throw new Error('No texts provided for embedding generation');
    }

    logger.debug(`Generating Ollama embeddings for ${texts.length} texts`);

    try {
      // Ensure the model is available
      await this.ensureModelAvailable();

      // Process texts individually (Ollama typically processes one at a time)
      const results: number[][] = [];

      for (const text of texts) {
        const embedding = await this.generateSingleEmbedding(text);
        results.push(embedding);
      }

      logger.debug(`Successfully generated ${results.length} Ollama embeddings`);
      return results;

    } catch (error) {
      logger.error('Error generating Ollama embeddings:', error);
      throw new Error(`Ollama embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embedding for a single text with retry logic
   */
  private async generateSingleEmbedding(text: string): Promise<number[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        logger.debug(`Ollama embedding attempt ${attempt} for text length: ${text.length}`);

        const response = await this.ollama.embeddings({
          model: this.model,
          prompt: text,
        });

        if (!response.embedding || !Array.isArray(response.embedding)) {
          throw new Error('Invalid embedding response from Ollama');
        }

        const embedding = response.embedding;

        // Cache the dimension if not already known
        if (this.modelDimension === null) {
          this.modelDimension = embedding.length;
          logger.info(`Detected Ollama model dimension: ${this.modelDimension}`);
        }

        return embedding;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        logger.warn(`Ollama embedding attempt ${attempt} failed:`, lastError.message);

        if (attempt < this.maxRetries) {
          // Check if it's a connection error
          if (lastError.message.includes('ECONNREFUSED') || lastError.message.includes('fetch failed')) {
            logger.warn('Connection to Ollama failed, retrying...');
            await this.sleep(this.retryDelay * attempt); // Longer delay for connection issues
          } else {
            await this.sleep(this.retryDelay);
          }
        }
      }
    }

    throw new Error(`Failed to generate Ollama embedding after ${this.maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * Ensure the model is available and pull it if necessary
   */
  private async ensureModelAvailable(): Promise<void> {
    try {
      // Check if model exists
      const models = await this.ollama.list();
      const modelExists = models.models.some(m => m.name === this.model || m.name.startsWith(this.model + ':'));

      if (!modelExists) {
        logger.info(`Model ${this.model} not found locally, attempting to pull...`);
        await this.pullModel();
      }

    } catch (error) {
      logger.warn('Could not check model availability:', error);
      // Continue anyway, let the embedding call fail if model is not available
    }
  }

  /**
   * Pull the model from Ollama registry
   */
  private async pullModel(): Promise<void> {
    try {
      logger.info(`Pulling Ollama model: ${this.model}`);

      const stream = await this.ollama.pull({
        model: this.model,
        stream: true
      });

      for await (const chunk of stream) {
        if (chunk.status) {
          logger.debug(`Pull status: ${chunk.status}`);
        }
        if (chunk.completed && chunk.total) {
          const progress = Math.round((chunk.completed / chunk.total) * 100);
          logger.debug(`Pull progress: ${progress}%`);
        }
      }

      logger.info(`Successfully pulled model: ${this.model}`);

    } catch (error) {
      logger.error(`Failed to pull model ${this.model}:`, error);
      throw new Error(`Could not pull Ollama model: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get the embedding dimension
   */
  getDimension(): number {
    // Return cached dimension or default
    return this.modelDimension || 768; // Common dimension for many embedding models
  }

  /**
   * Get the model name
   */
  getModelName(): string {
    return this.model;
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate Ollama connection and model availability
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      logger.debug('Validating Ollama configuration...');

      // Test connection
      await this.ollama.list();

      // Test embedding generation
      const testText = "This is a test sentence for validation.";
      await this.generateSingleEmbedding(testText);

      logger.info('Ollama configuration validated successfully');
      return true;

    } catch (error) {
      logger.error('Ollama configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Get model information from Ollama
   */
  async getModelInfo(): Promise<any> {
    try {
      const models = await this.ollama.list();
      const modelInfo = models.models.find(m => m.name === this.model || m.name.startsWith(this.model + ':'));

      if (modelInfo) {
        return {
          model: this.model,
          name: modelInfo.name,
          size: modelInfo.size,
          digest: modelInfo.digest,
          modified_at: modelInfo.modified_at,
          dimension: this.modelDimension,
          provider: 'ollama',
          status: 'available'
        };
      } else {
        return {
          model: this.model,
          provider: 'ollama',
          status: 'not_found',
          dimension: this.modelDimension
        };
      }
    } catch (error) {
      logger.error('Error getting Ollama model info:', error);
      throw error;
    }
  }

  /**
   * Check if the model supports batch processing
   */
  supportsBatchProcessing(): boolean {
    return false; // Ollama typically processes one embedding at a time
  }

  /**
   * Get recommended batch size for this provider
   */
  getRecommendedBatchSize(): number {
    return 1; // Process one at a time
  }

  /**
   * Estimate cost for embedding generation
   */
  estimateCost(textCount: number): { currency: string; amount: number } {
    // Ollama is free (local processing)
    return {
      currency: 'USD',
      amount: 0
    };
  }

  /**
   * Get provider-specific metrics
   */
  getMetrics(): {
    provider: string;
    model: string;
    dimension: number;
    maxRetries: number;
    retryDelay: number;
    baseUrl: string;
  } {
    return {
      provider: 'ollama',
      model: this.model,
      dimension: this.getDimension(),
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay,
      baseUrl: config.ollama.baseUrl
    };
  }

  /**
   * Check Ollama server health
   */
  async checkHealth(): Promise<{ status: string; version?: string; error?: string }> {
    try {
      // Ollama doesn't have a dedicated health endpoint, so we use list
      await this.ollama.list();
      return { status: 'healthy' };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get available models from Ollama
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const models = await this.ollama.list();
      return models.models.map(m => m.name);
    } catch (error) {
      logger.error('Error getting available Ollama models:', error);
      return [];
    }
  }
}
