import express, { Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/middleware/errorHandler';
import { TextChunkingService } from '@/services/textChunkingService';
import { TextPreprocessingService } from '@/services/textPreprocessingService';
import { MetadataExtractionService } from '@/services/metadataExtractionService';
import { ChunkingConfigService } from '@/config/chunkingConfig';
import {
  ChunkingStrategy,
  ChunkingConfig,
  TextPreprocessingOptions
} from '@/types/document';
import { logger } from '@/utils/logger';

const router = express.Router();

// Service instances
const chunkingService = TextChunkingService.getInstance();
const preprocessingService = TextPreprocessingService.getInstance();
const metadataService = MetadataExtractionService.getInstance();
const configService = ChunkingConfigService.getInstance();

/**
 * @route POST /api/chunking/chunk-text
 * @desc Chunk text using specified configuration
 * @access Public
 */
router.post('/chunk-text', asyncHandler(async (req: Request, res: Response) => {
  const {
    text,
    config = {},
    preprocessing = {},
    sourceMetadata = {},
    extractMetadata = true
  } = req.body;

  if (!text || typeof text !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Text is required and must be a string'
    });
  }

  try {
    // Validate configuration
    const chunkingConfig = { ...configService.getDefaultConfig(), ...config };
    const validation = configService.validateConfig(chunkingConfig);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid chunking configuration',
        details: validation.errors
      });
    }

    // Chunk the text
    const chunks = chunkingService.chunkText(
      text,
      chunkingConfig,
      preprocessing,
      sourceMetadata
    );

    // Extract metadata if requested
    const enrichedChunks = extractMetadata
      ? chunks.map(chunk => metadataService.extractChunkMetadata(chunk, sourceMetadata, text))
      : chunks;

    return res.json({
      success: true,
      data: {
        chunks: enrichedChunks,
        totalChunks: enrichedChunks.length,
        originalLength: text.length,
        config: chunkingConfig,
        preprocessing
      }
    });

  } catch (error) {
    logger.error('Error chunking text:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to chunk text',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * @route POST /api/chunking/preprocess-text
 * @desc Preprocess text using specified options
 * @access Public
 */
router.post('/preprocess-text', asyncHandler(async (req: Request, res: Response) => {
  const { text, options = {} } = req.body;

  if (!text || typeof text !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Text is required and must be a string'
    });
  }

  try {
    const processedText = preprocessingService.preprocessText(text, options);
    const validation = preprocessingService.validateTextQuality(text, processedText);

    return res.json({
      success: true,
      data: {
        originalText: text,
        processedText,
        options,
        validation,
        metrics: {
          originalLength: text.length,
          processedLength: processedText.length,
          lengthReduction: ((text.length - processedText.length) / text.length) * 100
        }
      }
    });

  } catch (error) {
    logger.error('Error preprocessing text:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to preprocess text',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * @route GET /api/chunking/config/default
 * @desc Get default chunking configuration
 * @access Public
 */
router.get('/config/default', asyncHandler(async (req: Request, res: Response) => {
  try {
    const config = configService.getDefaultConfig();
    const preprocessing = configService.getDefaultPreprocessingOptions();

    return res.json({
      success: true,
      data: {
        chunking: config,
        preprocessing
      }
    });

  } catch (error) {
    logger.error('Error getting default config:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get default configuration'
    });
  }
}));

/**
 * @route GET /api/chunking/config/document-type/:type
 * @desc Get configuration for specific document type
 * @access Public
 */
router.get('/config/document-type/:type', asyncHandler(async (req: Request, res: Response) => {
  const { type } = req.params;
  const { useCase } = req.query;

  try {
    let config: ChunkingConfig;

    if (useCase && typeof useCase === 'string') {
      config = configService.getConfigForUseCase(useCase);
    } else {
      config = configService.getConfigForDocumentType(type);
    }

    const preprocessing = configService.getPreprocessingForDocumentType(type);

    return res.json({
      success: true,
      data: {
        documentType: type,
        useCase: useCase || 'default',
        chunking: config,
        preprocessing
      }
    });

  } catch (error) {
    logger.error('Error getting document type config:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get document type configuration'
    });
  }
}));

/**
 * @route POST /api/chunking/config/validate
 * @desc Validate chunking configuration
 * @access Public
 */
router.post('/config/validate', asyncHandler(async (req: Request, res: Response) => {
  const { config } = req.body;

  if (!config) {
    return res.status(400).json({
      success: false,
      error: 'Configuration is required'
    });
  }

  try {
    const validation = configService.validateConfig(config);

    return res.json({
      success: true,
      data: {
        isValid: validation.isValid,
        errors: validation.errors,
        config
      }
    });

  } catch (error) {
    logger.error('Error validating config:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to validate configuration'
    });
  }
}));

/**
 * @route POST /api/chunking/analyze-text
 * @desc Analyze text and extract metadata without chunking
 * @access Public
 */
router.post('/analyze-text', asyncHandler(async (req: Request, res: Response) => {
  const { text, sourceMetadata = {} } = req.body;

  if (!text || typeof text !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Text is required and must be a string'
    });
  }

  try {
    // Create a temporary chunk for analysis
    const tempChunk = {
      id: 'analysis-chunk',
      text,
      startIndex: 0,
      endIndex: text.length,
      metadata: {}
    };

    const analysis = metadataService.extractChunkMetadata(tempChunk, sourceMetadata, text);

    return res.json({
      success: true,
      data: {
        text,
        analysis: analysis.metadata,
        recommendations: {
          optimalChunkSize: chunkingService.getOptimalChunkSize(
            chunkingService.estimateTokenCount(text)
          ),
          suggestedStrategy: analysis.metadata?.contentType?.type === 'code' ?
            ChunkingStrategy.RECURSIVE_CHARACTER :
            ChunkingStrategy.PARAGRAPH
        }
      }
    });

  } catch (error) {
    logger.error('Error analyzing text:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to analyze text'
    });
  }
}));

/**
 * @route GET /api/chunking/strategies
 * @desc Get available chunking strategies
 * @access Public
 */
router.get('/strategies', asyncHandler(async (req: Request, res: Response) => {
  try {
    const strategies = Object.values(ChunkingStrategy).map(strategy => ({
      value: strategy,
      label: strategy.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: getStrategyDescription(strategy)
    }));

    return res.json({
      success: true,
      data: {
        strategies,
        default: ChunkingStrategy.RECURSIVE_CHARACTER
      }
    });

  } catch (error) {
    logger.error('Error getting strategies:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get chunking strategies'
    });
  }
}));

/**
 * @route POST /api/chunking/test-performance
 * @desc Test chunking performance with different configurations
 * @access Public
 */
router.post('/test-performance', asyncHandler(async (req: Request, res: Response) => {
  const { text, configs = [] } = req.body;

  if (!text || typeof text !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Text is required and must be a string'
    });
  }

  if (!Array.isArray(configs) || configs.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'At least one configuration is required'
    });
  }

  try {
    const results = [];

    for (const config of configs) {
      const startTime = Date.now();

      const chunks = chunkingService.chunkText(text, config);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      results.push({
        config,
        performance: {
          processingTime,
          chunkCount: chunks.length,
          averageChunkSize: chunks.length > 0 ?
            chunks.reduce((sum, chunk) => sum + chunk.text.length, 0) / chunks.length : 0,
          memoryUsage: process.memoryUsage().heapUsed
        },
        chunks: chunks.slice(0, 3) // Return first 3 chunks as examples
      });
    }

    return res.json({
      success: true,
      data: {
        textLength: text.length,
        results,
        fastest: results.reduce((fastest, current) =>
          current.performance.processingTime < fastest.performance.processingTime ? current : fastest
        )
      }
    });

  } catch (error) {
    logger.error('Error testing performance:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to test performance'
    });
  }
}));

/**
 * Helper function to get strategy descriptions
 */
function getStrategyDescription(strategy: ChunkingStrategy): string {
  switch (strategy) {
    case ChunkingStrategy.CHARACTER:
      return 'Simple character-based splitting with fixed chunk sizes';
    case ChunkingStrategy.RECURSIVE_CHARACTER:
      return 'Hierarchical splitting using multiple separators (paragraphs, sentences, words, characters)';
    case ChunkingStrategy.SENTENCE:
      return 'Sentence-based chunking with configurable window size';
    case ChunkingStrategy.PARAGRAPH:
      return 'Paragraph-aware chunking that preserves paragraph boundaries';
    case ChunkingStrategy.SEMANTIC:
      return 'Semantic chunking based on content meaning (experimental)';
    case ChunkingStrategy.DOCUMENT_SPECIFIC:
      return 'Document-type specific chunking optimized for the content format';
    default:
      return 'Unknown chunking strategy';
  }
}

export default router;
