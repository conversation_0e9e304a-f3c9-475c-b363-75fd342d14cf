import fs from 'fs';
import path from 'path';
import Tesseract from 'tesseract.js';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  ImageMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class ImageProcessor implements IDocumentProcessor {
  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType.startsWith('image/') ||
           ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff'].includes(mimeType);
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.IMAGE;
  }

  /**
   * Process an image file and extract text content using OCR
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing image file: ${filePath}`);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`Image file not found: ${filePath}`);
      }

      // Extract basic metadata first
      const metadata = await this.extractMetadata(filePath);

      // Perform OCR
      const ocrResult = await this.performOCR(filePath, options?.ocrLanguage || 'eng');

      // Extract text and confidence
      const text = ocrResult.data.text.trim();
      const confidence = ocrResult.data.confidence;

      // Update metadata with OCR results
      metadata.hasText = text.length > 0;
      metadata.confidence = confidence;
      metadata.ocrLanguage = options?.ocrLanguage || 'eng';
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;

      // Create chunks if requested
      const chunks = options?.chunkSize && text.length > 0 ? 
        this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) : 
        undefined;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`Image OCR processing completed: ${filePath} (${result.processingTime}ms, confidence: ${confidence}%)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
      logger.error(`Image processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Perform OCR on the image
   */
  private async performOCR(filePath: string, language: string): Promise<Tesseract.RecognizeResult> {
    try {
      logger.debug(`Starting OCR for ${filePath} with language: ${language}`);

      const result = await Tesseract.recognize(
        filePath,
        language,
        {
          logger: (m) => {
            if (m.status === 'recognizing text') {
              logger.debug(`OCR progress: ${Math.round(m.progress * 100)}%`);
            }
          }
        }
      );

      return result;
    } catch (error) {
      logger.error(`OCR failed for ${filePath}:`, error);
      throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract metadata from image file
   */
  private async extractMetadata(filePath: string): Promise<ImageMetadata> {
    try {
      const stats = fs.statSync(filePath);
      const ext = path.extname(filePath).toLowerCase();

      // Get basic image information
      const format = this.getImageFormat(ext);

      const metadata: ImageMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: this.getMimeType(ext),
        size: stats.size,
        type: DocumentType.IMAGE,
        format,
        hasText: false,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      // Try to get image dimensions (basic implementation)
      try {
        const dimensions = await this.getImageDimensions(filePath);
        metadata.width = dimensions.width;
        metadata.height = dimensions.height;
      } catch (error) {
        logger.debug(`Could not extract image dimensions for ${filePath}:`, error);
      }

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract image metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Get image format from extension
   */
  private getImageFormat(ext: string): string {
    const formatMap: Record<string, string> = {
      '.jpg': 'JPEG',
      '.jpeg': 'JPEG',
      '.png': 'PNG',
      '.gif': 'GIF',
      '.bmp': 'BMP',
      '.tiff': 'TIFF',
      '.tif': 'TIFF'
    };

    return formatMap[ext] || 'UNKNOWN';
  }

  /**
   * Get MIME type from extension
   */
  private getMimeType(ext: string): string {
    const mimeMap: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.tiff': 'image/tiff',
      '.tif': 'image/tiff'
    };

    return mimeMap[ext] || 'image/unknown';
  }

  /**
   * Get image dimensions (basic implementation)
   */
  private async getImageDimensions(filePath: string): Promise<{ width: number; height: number }> {
    // This is a simplified implementation
    // In a production environment, you might want to use a library like 'sharp' or 'image-size'
    return new Promise((resolve, reject) => {
      try {
        // For now, return default dimensions
        // TODO: Implement proper image dimension detection
        resolve({ width: 0, height: 0 });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Create text chunks from extracted text
   */
  private createChunks(text: string, chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];
    let currentPos = 0;

    while (currentPos < text.length) {
      const endPos = Math.min(currentPos + chunkSize, text.length);
      const chunkText = text.substring(currentPos, endPos);

      chunks.push({
        id: `image_ocr_chunk_${chunks.length}`,
        text: chunkText,
        startIndex: currentPos,
        endIndex: endPos,
        metadata: {
          source: 'image_ocr',
          chunkIndex: chunks.length
        }
      });

      // Move to next chunk with overlap
      currentPos = endPos - overlap;
      if (currentPos >= text.length) break;
    }

    return chunks;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): ImageMetadata {
    const stats = fs.statSync(filePath);
    const ext = path.extname(filePath).toLowerCase();

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: this.getMimeType(ext),
      size: stats.size,
      type: DocumentType.IMAGE,
      format: this.getImageFormat(ext),
      hasText: false,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): ImageMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
