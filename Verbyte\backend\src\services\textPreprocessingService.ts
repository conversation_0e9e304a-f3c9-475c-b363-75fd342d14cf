import { logger } from '@/utils/logger';
import { TextPreprocessingOptions } from '@/types/document';

/**
 * Text Preprocessing Service
 * 
 * Provides comprehensive text cleaning, normalization, and preprocessing
 * functions for optimal text chunking and processing.
 */
export class TextPreprocessingService {
  private static instance: TextPreprocessingService;

  // Common patterns for text cleaning
  private static readonly CONTROL_CHARS_REGEX = /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g;
  private static readonly MULTIPLE_SPACES_REGEX = /[ \t]+/g;
  private static readonly MULTIPLE_NEWLINES_REGEX = /\n\s*\n\s*\n/g;
  private static readonly WHITESPACE_REGEX = /\s+/g;
  private static readonly UNICODE_QUOTES_REGEX = /[\u2018\u2019\u201C\u201D]/g;
  private static readonly UNICODE_DASHES_REGEX = /[\u2013\u2014]/g;
  private static readonly ZERO_WIDTH_CHARS_REGEX = /[\u200B\u200C\u200D\uFEFF]/g;

  // Language-specific patterns
  private static readonly LANGUAGE_PATTERNS = {
    // Common English contractions
    contractions: {
      "won't": "will not",
      "can't": "cannot",
      "n't": " not",
      "'re": " are",
      "'ve": " have",
      "'ll": " will",
      "'d": " would",
      "'m": " am"
    },
    
    // Common abbreviations that should be preserved
    abbreviations: [
      'Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.',
      'Inc.', 'Ltd.', 'Corp.', 'Co.', 'LLC',
      'U.S.', 'U.K.', 'U.S.A.', 'etc.', 'vs.', 'e.g.', 'i.e.'
    ]
  };

  public static getInstance(): TextPreprocessingService {
    if (!TextPreprocessingService.instance) {
      TextPreprocessingService.instance = new TextPreprocessingService();
    }
    return TextPreprocessingService.instance;
  }

  /**
   * Main preprocessing method
   */
  public preprocessText(
    text: string,
    options: Partial<TextPreprocessingOptions> = {}
  ): string {
    const defaultOptions: TextPreprocessingOptions = {
      normalizeWhitespace: true,
      removeExtraSpaces: true,
      normalizeUnicode: false,
      preserveLineBreaks: true,
      removeControlCharacters: true,
      trimChunks: true
    };

    const fullOptions = { ...defaultOptions, ...options };
    let processed = text;

    logger.debug('Starting text preprocessing with options:', fullOptions);

    // Step 1: Remove control characters
    if (fullOptions.removeControlCharacters) {
      processed = this.removeControlCharacters(processed);
    }

    // Step 2: Normalize Unicode
    if (fullOptions.normalizeUnicode) {
      processed = this.normalizeUnicode(processed);
    }

    // Step 3: Normalize whitespace
    if (fullOptions.normalizeWhitespace) {
      processed = this.normalizeWhitespace(processed, fullOptions.preserveLineBreaks);
    }

    // Step 4: Remove extra spaces
    if (fullOptions.removeExtraSpaces) {
      processed = this.removeExtraSpaces(processed);
    }

    // Step 5: Trim if requested
    if (fullOptions.trimChunks) {
      processed = processed.trim();
    }

    logger.debug(`Text preprocessing completed. Original length: ${text.length}, Processed length: ${processed.length}`);
    return processed;
  }

  /**
   * Remove control characters except essential ones
   */
  private removeControlCharacters(text: string): string {
    // Remove control characters but preserve newlines (\n), carriage returns (\r), and tabs (\t)
    return text.replace(TextPreprocessingService.CONTROL_CHARS_REGEX, '');
  }

  /**
   * Normalize Unicode characters
   */
  private normalizeUnicode(text: string): string {
    let normalized = text;

    // Normalize Unicode form
    normalized = normalized.normalize('NFC');

    // Replace Unicode quotes with standard quotes
    normalized = normalized.replace(TextPreprocessingService.UNICODE_QUOTES_REGEX, (match) => {
      switch (match) {
        case '\u2018': // Left single quotation mark
        case '\u2019': // Right single quotation mark
          return "'";
        case '\u201C': // Left double quotation mark
        case '\u201D': // Right double quotation mark
          return '"';
        default:
          return match;
      }
    });

    // Replace Unicode dashes with standard dashes
    normalized = normalized.replace(TextPreprocessingService.UNICODE_DASHES_REGEX, '-');

    // Remove zero-width characters
    normalized = normalized.replace(TextPreprocessingService.ZERO_WIDTH_CHARS_REGEX, '');

    return normalized;
  }

  /**
   * Normalize whitespace characters
   */
  private normalizeWhitespace(text: string, preserveLineBreaks: boolean = true): string {
    if (preserveLineBreaks) {
      // Normalize spaces and tabs but preserve line structure
      return text.replace(TextPreprocessingService.MULTIPLE_SPACES_REGEX, ' ');
    } else {
      // Normalize all whitespace to single spaces
      return text.replace(TextPreprocessingService.WHITESPACE_REGEX, ' ');
    }
  }

  /**
   * Remove extra spaces and normalize line breaks
   */
  private removeExtraSpaces(text: string): string {
    let processed = text;

    // Remove leading/trailing spaces from lines
    processed = processed.split('\n')
      .map(line => line.trim())
      .join('\n');

    // Remove multiple consecutive newlines (more than 2)
    processed = processed.replace(TextPreprocessingService.MULTIPLE_NEWLINES_REGEX, '\n\n');

    return processed;
  }

  /**
   * Clean text for specific document types
   */
  public cleanDocumentSpecificText(text: string, documentType: string): string {
    let cleaned = text;

    switch (documentType.toLowerCase()) {
      case 'pdf':
        cleaned = this.cleanPdfText(cleaned);
        break;
      case 'docx':
      case 'doc':
        cleaned = this.cleanWordText(cleaned);
        break;
      case 'html':
        cleaned = this.cleanHtmlText(cleaned);
        break;
      case 'markdown':
      case 'md':
        cleaned = this.cleanMarkdownText(cleaned);
        break;
      case 'csv':
        cleaned = this.cleanCsvText(cleaned);
        break;
      default:
        // Apply general cleaning
        cleaned = this.cleanGeneralText(cleaned);
    }

    return cleaned;
  }

  /**
   * Clean PDF-specific artifacts
   */
  private cleanPdfText(text: string): string {
    let cleaned = text;

    // Remove common PDF artifacts
    cleaned = cleaned.replace(/\f/g, '\n'); // Form feed to newline
    cleaned = cleaned.replace(/\u00A0/g, ' '); // Non-breaking space to regular space
    
    // Remove page numbers (simple pattern)
    cleaned = cleaned.replace(/^\s*\d+\s*$/gm, '');
    
    // Fix broken words across lines (simple heuristic)
    cleaned = cleaned.replace(/([a-z])-\s*\n\s*([a-z])/g, '$1$2');
    
    return cleaned;
  }

  /**
   * Clean Word document artifacts
   */
  private cleanWordText(text: string): string {
    let cleaned = text;

    // Remove Word-specific artifacts
    cleaned = cleaned.replace(/\r\n/g, '\n'); // Normalize line endings
    cleaned = cleaned.replace(/\u00A0/g, ' '); // Non-breaking space
    
    // Remove excessive formatting artifacts
    cleaned = cleaned.replace(/_{3,}/g, ''); // Remove underlines
    cleaned = cleaned.replace(/-{3,}/g, '---'); // Normalize dashes
    
    return cleaned;
  }

  /**
   * Clean HTML artifacts
   */
  private cleanHtmlText(text: string): string {
    let cleaned = text;

    // Remove HTML entities (basic ones)
    const htmlEntities: Record<string, string> = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&nbsp;': ' '
    };

    Object.entries(htmlEntities).forEach(([entity, replacement]) => {
      cleaned = cleaned.replace(new RegExp(entity, 'g'), replacement);
    });

    return cleaned;
  }

  /**
   * Clean Markdown artifacts
   */
  private cleanMarkdownText(text: string): string {
    let cleaned = text;

    // Preserve markdown structure but clean artifacts
    // Remove excessive blank lines while preserving structure
    cleaned = cleaned.replace(/\n{4,}/g, '\n\n\n');
    
    return cleaned;
  }

  /**
   * Clean CSV artifacts
   */
  private cleanCsvText(text: string): string {
    let cleaned = text;

    // Remove quotes around fields if they exist
    cleaned = cleaned.replace(/^"|"$/gm, '');
    
    // Normalize delimiters
    cleaned = cleaned.replace(/,\s+/g, ', ');
    
    return cleaned;
  }

  /**
   * General text cleaning
   */
  private cleanGeneralText(text: string): string {
    let cleaned = text;

    // Remove excessive punctuation
    cleaned = cleaned.replace(/[.]{3,}/g, '...');
    cleaned = cleaned.replace(/[!]{2,}/g, '!');
    cleaned = cleaned.replace(/[?]{2,}/g, '?');
    
    // Normalize quotes
    cleaned = cleaned.replace(/[""]/g, '"');
    cleaned = cleaned.replace(/['']/g, "'");
    
    return cleaned;
  }

  /**
   * Expand contractions for better text processing
   */
  public expandContractions(text: string): string {
    let expanded = text;
    
    Object.entries(TextPreprocessingService.LANGUAGE_PATTERNS.contractions).forEach(([contraction, expansion]) => {
      const regex = new RegExp(contraction.replace(/'/g, "'?"), 'gi');
      expanded = expanded.replace(regex, expansion);
    });
    
    return expanded;
  }

  /**
   * Detect and preserve important formatting
   */
  public preserveImportantFormatting(text: string): { text: string; formatting: any[] } {
    const formatting: any[] = [];
    let processed = text;

    // Preserve URLs
    const urlRegex = /https?:\/\/[^\s]+/g;
    let match;
    while ((match = urlRegex.exec(text)) !== null) {
      formatting.push({
        type: 'url',
        start: match.index,
        end: match.index + match[0].length,
        value: match[0]
      });
    }

    // Preserve email addresses
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    while ((match = emailRegex.exec(text)) !== null) {
      formatting.push({
        type: 'email',
        start: match.index,
        end: match.index + match[0].length,
        value: match[0]
      });
    }

    // Preserve phone numbers (basic pattern)
    const phoneRegex = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g;
    while ((match = phoneRegex.exec(text)) !== null) {
      formatting.push({
        type: 'phone',
        start: match.index,
        end: match.index + match[0].length,
        value: match[0]
      });
    }

    return { text: processed, formatting };
  }

  /**
   * Validate text quality after preprocessing
   */
  public validateTextQuality(originalText: string, processedText: string): {
    isValid: boolean;
    issues: string[];
    metrics: {
      lengthReduction: number;
      characterLoss: number;
      wordLoss: number;
    };
  } {
    const issues: string[] = [];
    const originalWords = originalText.split(/\s+/).length;
    const processedWords = processedText.split(/\s+/).length;
    
    const metrics = {
      lengthReduction: ((originalText.length - processedText.length) / originalText.length) * 100,
      characterLoss: originalText.length - processedText.length,
      wordLoss: originalWords - processedWords
    };

    // Check for excessive length reduction
    if (metrics.lengthReduction > 50) {
      issues.push('Excessive text reduction detected (>50%)');
    }

    // Check for significant word loss
    if (metrics.wordLoss > originalWords * 0.3) {
      issues.push('Significant word loss detected (>30%)');
    }

    // Check if processed text is too short
    if (processedText.length < 10) {
      issues.push('Processed text is too short');
    }

    return {
      isValid: issues.length === 0,
      issues,
      metrics
    };
  }
}
