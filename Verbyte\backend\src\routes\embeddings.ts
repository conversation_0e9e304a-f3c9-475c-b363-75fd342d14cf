import { Router, Request, Response } from 'express';
import { getEmbeddingService } from '../services/embeddingService';
import { logger } from '../utils/logger';
import { EmbeddingRequest, EmbeddingResponse } from '../types/document';

// Import express-validator functions
const expressValidator = require('express-validator');
const { body, validationResult } = expressValidator;

const router = Router();

/**
 * Validation middleware for embedding requests
 */
const validateEmbeddingRequest = [
  body('input')
    .custom((value: any) => {
      if (typeof value === 'string') {
        return value.trim().length > 0;
      }
      if (Array.isArray(value)) {
        return value.length > 0 && value.every((item: any) => typeof item === 'string' && item.trim().length > 0);
      }
      return false;
    })
    .withMessage('Input must be a non-empty string or array of non-empty strings'),

  body('model')
    .optional()
    .isString()
    .withMessage('Model must be a string'),
];

/**
 * Generate embeddings for text
 * POST /v1/embeddings
 *
 * Request body:
 * {
 *   "input": "text to embed" | ["text1", "text2", ...],
 *   "model": "optional-model-override"
 * }
 *
 * Response:
 * {
 *   "object": "list",
 *   "model": "model-name",
 *   "data": [
 *     {
 *       "index": 0,
 *       "object": "embedding",
 *       "embedding": [0.1, 0.2, ...]
 *     }
 *   ],
 *   "usage": {
 *     "prompt_tokens": 10,
 *     "total_tokens": 10
 *   }
 * }
 */
router.post('/', validateEmbeddingRequest, async (req: Request, res: Response): Promise<void> => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Invalid request parameters',
        errors: errors.array(),
        code: 'VALIDATION_ERROR'
      });
      return;
    }

    const { input, model } = req.body as EmbeddingRequest;

    logger.info(`Embedding request received for ${Array.isArray(input) ? input.length : 1} text(s)`);

    // Get the embedding service
    const embeddingService = getEmbeddingService();

    // Generate embeddings
    const response: EmbeddingResponse = await embeddingService.createEmbeddingResponse(input);

    logger.info(`Successfully generated embeddings using ${response.model}`);

    res.json(response);

  } catch (error) {
    logger.error('Error in embedding generation endpoint:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    res.status(500).json({
      status: 'error',
      message: 'Failed to generate embeddings',
      details: errorMessage,
      code: 'EMBEDDING_GENERATION_ERROR'
    });
  }
});

export { router as embeddingsRoutes };
