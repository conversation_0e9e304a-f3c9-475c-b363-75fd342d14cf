import fs from 'fs';
import path from 'path';
import * as pdfjsLib from 'pdfjs-dist';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  PDFMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class PDFProcessor implements IDocumentProcessor {
  constructor() {
    // Configure PDF.js for Node.js environment
    try {
      pdfjsLib.GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.js');
    } catch (error) {
      // Fallback for different pdfjs-dist versions
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdfjs-dist/build/pdf.worker.js';
    }
  }

  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'application/pdf' || mimeType.includes('pdf');
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.PDF;
  }

  /**
   * Process a PDF file and extract text content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing PDF file: ${filePath}`);

      // Read the PDF file
      const data = new Uint8Array(fs.readFileSync(filePath));

      // Load the PDF document
      const pdfDocument = await pdfjsLib.getDocument({
        data,
        verbosity: 0 // Suppress PDF.js warnings
      }).promise;

      // Extract metadata
      const metadata = await this.extractMetadata(pdfDocument, filePath);

      // Extract text from all pages
      const { text, chunks } = await this.extractTextFromPages(pdfDocument, options);

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks: options?.chunkSize ? chunks : undefined,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`PDF processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown PDF processing error';
      logger.error(`PDF processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Extract metadata from PDF document
   */
  private async extractMetadata(pdfDocument: any, filePath: string): Promise<PDFMetadata> {
    try {
      const info = await pdfDocument.getMetadata();
      const stats = fs.statSync(filePath);

      const metadata: PDFMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'application/pdf',
        size: stats.size,
        type: DocumentType.PDF,
        pages: pdfDocument.numPages,
        pdfVersion: info.info?.PDFFormatVersion || 'unknown',
        isEncrypted: pdfDocument.isEncrypted || false,
        title: info.info?.Title || undefined,
        author: info.info?.Author || undefined,
        subject: info.info?.Subject || undefined,
        keywords: info.info?.Keywords ? info.info.Keywords.split(',').map((k: string) => k.trim()) : undefined,
        creator: info.info?.Creator || undefined,
        producer: info.info?.Producer || undefined,
        createdAt: info.info?.CreationDate ? new Date(info.info.CreationDate) : undefined,
        modifiedAt: info.info?.ModDate ? new Date(info.info.ModDate) : undefined
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract PDF metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Extract text from all pages of the PDF
   */
  private async extractTextFromPages(
    pdfDocument: any,
    options?: ProcessingOptions
  ): Promise<{ text: string; chunks: TextChunk[] }> {
    const textParts: string[] = [];
    const chunks: TextChunk[] = [];
    let currentIndex = 0;

    for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
      try {
        const page = await pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Extract text items and join them
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim();

        if (pageText) {
          textParts.push(pageText);

          // Create chunks if requested
          if (options?.chunkSize) {
            const pageChunks = this.createChunks(
              pageText,
              currentIndex,
              pageNum,
              options.chunkSize,
              options.chunkOverlap || 0
            );
            chunks.push(...pageChunks);
            currentIndex += pageText.length + 1; // +1 for page separator
          }
        }

        logger.debug(`Extracted text from page ${pageNum}/${pdfDocument.numPages}: ${pageText.length} characters`);
      } catch (error) {
        logger.warn(`Failed to extract text from page ${pageNum}:`, error);
        // Continue with other pages
      }
    }

    const fullText = textParts.join('\n\n');
    return { text: fullText, chunks };
  }

  /**
   * Create text chunks from extracted text
   */
  private createChunks(
    text: string,
    startIndex: number,
    page: number,
    chunkSize: number,
    overlap: number
  ): TextChunk[] {
    const chunks: TextChunk[] = [];
    let currentPos = 0;

    while (currentPos < text.length) {
      const endPos = Math.min(currentPos + chunkSize, text.length);
      const chunkText = text.substring(currentPos, endPos);

      chunks.push({
        id: `pdf_page_${page}_chunk_${chunks.length}`,
        text: chunkText,
        startIndex: startIndex + currentPos,
        endIndex: startIndex + endPos,
        page,
        metadata: {
          source: 'pdf',
          page,
          chunkIndex: chunks.length
        }
      });

      // Move to next chunk with overlap
      currentPos = endPos - overlap;
      if (currentPos >= text.length) break;
    }

    return chunks;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): PDFMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'application/pdf',
      size: stats.size,
      type: DocumentType.PDF,
      pages: 0,
      pdfVersion: 'unknown',
      isEncrypted: false
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): PDFMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
