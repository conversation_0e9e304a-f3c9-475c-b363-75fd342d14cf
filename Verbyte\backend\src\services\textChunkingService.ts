import { logger } from '@/utils/logger';
import {
  TextChunk,
  ChunkingStrategy,
  ChunkingConfig,
  TextPreprocessingOptions
} from '@/types/document';

/**
 * Unified Text Chunking Service
 * 
 * Provides configurable text splitting algorithms equivalent to Python's
 * SentenceWindowNodeParser and RecursiveCharacterTextSplitter from llama-index.
 */
export class TextChunkingService {
  private static instance: TextChunkingService;

  // Default separators for recursive character splitting (similar to langchain)
  private static readonly DEFAULT_SEPARATORS = [
    '\n\n',    // Double newline (paragraphs)
    '\n',      // Single newline
    ' ',       // Space
    ''         // Character level
  ];

  // Sentence boundary patterns
  private static readonly SENTENCE_ENDINGS = /[.!?]+(?:\s+|$)/g;

  // Default chunking configuration
  private static readonly DEFAULT_CONFIG: ChunkingConfig = {
    strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
    chunkSize: 1000,
    chunkOverlap: 200,
    separators: TextChunkingService.DEFAULT_SEPARATORS,
    keepSeparator: false,
    lengthFunction: (text: string) => text.length,
    sentenceWindowSize: 3,
    preserveFormatting: true,
    respectWordBoundaries: true,
    minChunkSize: 50,
    maxChunkSize: 4000
  };

  private static readonly DEFAULT_PREPROCESSING: TextPreprocessingOptions = {
    normalizeWhitespace: true,
    removeExtraSpaces: true,
    normalizeUnicode: false,
    preserveLineBreaks: true,
    removeControlCharacters: true,
    trimChunks: true
  };

  public static getInstance(): TextChunkingService {
    if (!TextChunkingService.instance) {
      TextChunkingService.instance = new TextChunkingService();
    }
    return TextChunkingService.instance;
  }

  /**
   * Main chunking method that delegates to specific strategies
   */
  public chunkText(
    text: string,
    config: Partial<ChunkingConfig> = {},
    preprocessing: Partial<TextPreprocessingOptions> = {},
    sourceMetadata: Record<string, any> = {}
  ): TextChunk[] {
    const fullConfig = { ...TextChunkingService.DEFAULT_CONFIG, ...config };
    const fullPreprocessing = { ...TextChunkingService.DEFAULT_PREPROCESSING, ...preprocessing };

    logger.debug(`Chunking text with strategy: ${fullConfig.strategy}, size: ${fullConfig.chunkSize}, overlap: ${fullConfig.chunkOverlap}`);

    // Preprocess text
    const processedText = this.preprocessText(text, fullPreprocessing);

    // Apply chunking strategy
    let chunks: TextChunk[];
    switch (fullConfig.strategy) {
      case ChunkingStrategy.CHARACTER:
        chunks = this.characterChunking(processedText, fullConfig, sourceMetadata);
        break;
      case ChunkingStrategy.RECURSIVE_CHARACTER:
        chunks = this.recursiveCharacterChunking(processedText, fullConfig, sourceMetadata);
        break;
      case ChunkingStrategy.SENTENCE:
        chunks = this.sentenceChunking(processedText, fullConfig, sourceMetadata);
        break;
      case ChunkingStrategy.PARAGRAPH:
        chunks = this.paragraphChunking(processedText, fullConfig, sourceMetadata);
        break;
      case ChunkingStrategy.SEMANTIC:
        chunks = this.semanticChunking(processedText, fullConfig, sourceMetadata);
        break;
      default:
        logger.warn(`Unknown chunking strategy: ${fullConfig.strategy}, falling back to recursive character`);
        chunks = this.recursiveCharacterChunking(processedText, fullConfig, sourceMetadata);
    }

    // Post-process chunks
    return this.postProcessChunks(chunks, fullConfig, fullPreprocessing);
  }

  /**
   * Preprocess text before chunking
   */
  private preprocessText(text: string, options: TextPreprocessingOptions): string {
    let processed = text;

    if (options.removeControlCharacters) {
      // Remove control characters except newlines and tabs
      processed = processed.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    }

    if (options.normalizeUnicode) {
      processed = processed.normalize('NFC');
    }

    if (options.normalizeWhitespace) {
      if (options.preserveLineBreaks) {
        // Normalize spaces but preserve line structure
        processed = processed.replace(/[ \t]+/g, ' ');
      } else {
        // Normalize all whitespace to single spaces
        processed = processed.replace(/\s+/g, ' ');
      }
    }

    if (options.removeExtraSpaces) {
      // Remove leading/trailing spaces from lines
      processed = processed.split('\n')
        .map(line => line.trim())
        .join('\n');

      // Remove multiple consecutive newlines
      processed = processed.replace(/\n\s*\n\s*\n/g, '\n\n');
    }

    return processed;
  }

  /**
   * Simple character-based chunking
   */
  private characterChunking(
    text: string,
    config: ChunkingConfig,
    sourceMetadata: Record<string, any>
  ): TextChunk[] {
    const chunks: TextChunk[] = [];
    let currentPos = 0;

    while (currentPos < text.length) {
      const endPos = Math.min(currentPos + config.chunkSize, text.length);
      let chunkText = text.substring(currentPos, endPos);

      // Respect word boundaries if enabled
      if (config.respectWordBoundaries && endPos < text.length) {
        const lastSpaceIndex = chunkText.lastIndexOf(' ');
        if (lastSpaceIndex > config.chunkSize * 0.8) { // Don't break if too close to start
          chunkText = chunkText.substring(0, lastSpaceIndex);
        }
      }

      chunks.push({
        id: `chunk_${chunks.length}`,
        text: chunkText,
        startIndex: currentPos,
        endIndex: currentPos + chunkText.length,
        metadata: {
          ...sourceMetadata,
          chunkIndex: chunks.length,
          strategy: config.strategy,
          type: 'character'
        }
      });

      // Move to next chunk with overlap
      currentPos = currentPos + chunkText.length - config.chunkOverlap;
      if (currentPos >= text.length) break;
    }

    return chunks;
  }

  /**
   * Recursive character chunking (similar to langchain's RecursiveCharacterTextSplitter)
   */
  private recursiveCharacterChunking(
    text: string,
    config: ChunkingConfig,
    sourceMetadata: Record<string, any>
  ): TextChunk[] {
    const separators = config.separators || TextChunkingService.DEFAULT_SEPARATORS;
    return this.splitTextRecursively(text, separators, config, sourceMetadata);
  }

  /**
   * Split text recursively using different separators
   */
  private splitTextRecursively(
    text: string,
    separators: string[],
    config: ChunkingConfig,
    sourceMetadata: Record<string, any>,
    currentIndex = 0
  ): TextChunk[] {
    const chunks: TextChunk[] = [];

    if (config.lengthFunction!(text) <= config.chunkSize) {
      // Text is small enough, create a single chunk
      if (text.trim().length > 0) {
        chunks.push({
          id: `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          text: text.trim(),
          startIndex: currentIndex,
          endIndex: currentIndex + text.length,
          metadata: {
            ...sourceMetadata,
            strategy: config.strategy,
            type: 'recursive'
          }
        });
      }
      return chunks;
    }

    // Try to split with the first separator
    const separator = separators[0];
    const splits = separator ? text.split(separator) : [text];

    if (splits.length === 1) {
      // Separator didn't split the text, try next separator
      if (separators.length > 1) {
        return this.splitTextRecursively(text, separators.slice(1), config, sourceMetadata, currentIndex);
      } else {
        // No more separators, force split by character
        return this.characterChunking(text, config, sourceMetadata);
      }
    }

    // Process splits
    let currentChunk = '';
    let chunkStartIndex = currentIndex;
    let textIndex = currentIndex;

    for (let i = 0; i < splits.length; i++) {
      const split = splits[i];
      const splitWithSeparator = config.keepSeparator && i < splits.length - 1 ?
        split + separator : split;

      if (config.lengthFunction!(currentChunk + splitWithSeparator) <= config.chunkSize) {
        currentChunk += splitWithSeparator;
      } else {
        // Current chunk would be too large
        if (currentChunk.trim().length > 0) {
          chunks.push({
            id: `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            text: currentChunk.trim(),
            startIndex: chunkStartIndex,
            endIndex: chunkStartIndex + currentChunk.length,
            metadata: {
              ...sourceMetadata,
              strategy: config.strategy,
              type: 'recursive'
            }
          });
        }

        // Start new chunk with overlap
        if (config.chunkOverlap > 0 && currentChunk.length > config.chunkOverlap) {
          const overlapText = currentChunk.substring(currentChunk.length - config.chunkOverlap);
          currentChunk = overlapText + splitWithSeparator;
          chunkStartIndex = textIndex - config.chunkOverlap;
        } else {
          currentChunk = splitWithSeparator;
          chunkStartIndex = textIndex;
        }
      }

      textIndex += split.length + (separator ? separator.length : 0);
    }

    // Add remaining chunk
    if (currentChunk.trim().length > 0) {
      chunks.push({
        id: `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        text: currentChunk.trim(),
        startIndex: chunkStartIndex,
        endIndex: chunkStartIndex + currentChunk.length,
        metadata: {
          ...sourceMetadata,
          strategy: config.strategy,
          type: 'recursive'
        }
      });
    }

    return chunks;
  }

  /**
   * Sentence-based chunking (similar to SentenceWindowNodeParser)
   */
  private sentenceChunking(
    text: string,
    config: ChunkingConfig,
    sourceMetadata: Record<string, any>
  ): TextChunk[] {
    const chunks: TextChunk[] = [];
    const sentences = this.splitIntoSentences(text);

    if (sentences.length === 0) {
      return chunks;
    }

    const windowSize = config.sentenceWindowSize || 3;
    let currentPos = 0;

    for (let i = 0; i < sentences.length; i += windowSize - (config.chunkOverlap > 0 ? 1 : 0)) {
      const endIndex = Math.min(i + windowSize, sentences.length);
      const windowSentences = sentences.slice(i, endIndex);
      const chunkText = windowSentences.join(' ').trim();

      if (chunkText.length > 0) {
        chunks.push({
          id: `sentence_chunk_${chunks.length}`,
          text: chunkText,
          startIndex: currentPos,
          endIndex: currentPos + chunkText.length,
          metadata: {
            ...sourceMetadata,
            chunkIndex: chunks.length,
            strategy: config.strategy,
            type: 'sentence',
            sentenceCount: windowSentences.length,
            sentenceRange: [i, endIndex - 1]
          }
        });
      }

      currentPos += chunkText.length + 1;
    }

    return chunks;
  }

  /**
   * Paragraph-based chunking
   */
  private paragraphChunking(
    text: string,
    config: ChunkingConfig,
    sourceMetadata: Record<string, any>
  ): TextChunk[] {
    const chunks: TextChunk[] = [];
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    if (paragraphs.length <= 1) {
      // Fall back to recursive character chunking
      return this.recursiveCharacterChunking(text, config, sourceMetadata);
    }

    let currentChunk = '';
    let currentStart = 0;
    let textIndex = 0;

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].trim();
      const potentialChunk = currentChunk.length > 0 ?
        currentChunk + '\n\n' + paragraph : paragraph;

      if (config.lengthFunction!(potentialChunk) <= config.chunkSize) {
        currentChunk = potentialChunk;
      } else {
        // Save current chunk if it exists
        if (currentChunk.length > 0) {
          chunks.push({
            id: `paragraph_chunk_${chunks.length}`,
            text: currentChunk,
            startIndex: currentStart,
            endIndex: currentStart + currentChunk.length,
            metadata: {
              ...sourceMetadata,
              chunkIndex: chunks.length,
              strategy: config.strategy,
              type: 'paragraph'
            }
          });
        }

        // Start new chunk with overlap
        if (config.chunkOverlap > 0 && currentChunk.length > config.chunkOverlap) {
          const overlapText = currentChunk.substring(currentChunk.length - config.chunkOverlap);
          currentChunk = overlapText + '\n\n' + paragraph;
          currentStart = textIndex - config.chunkOverlap;
        } else {
          currentChunk = paragraph;
          currentStart = textIndex;
        }
      }

      textIndex += paragraph.length + 2; // +2 for \n\n
    }

    // Add remaining chunk
    if (currentChunk.length > 0) {
      chunks.push({
        id: `paragraph_chunk_${chunks.length}`,
        text: currentChunk,
        startIndex: currentStart,
        endIndex: currentStart + currentChunk.length,
        metadata: {
          ...sourceMetadata,
          chunkIndex: chunks.length,
          strategy: config.strategy,
          type: 'paragraph'
        }
      });
    }

    return chunks;
  }

  /**
   * Semantic chunking (placeholder for future implementation)
   */
  private semanticChunking(
    text: string,
    config: ChunkingConfig,
    sourceMetadata: Record<string, any>
  ): TextChunk[] {
    logger.warn('Semantic chunking not yet implemented, falling back to sentence chunking');
    return this.sentenceChunking(text, config, sourceMetadata);
  }

  /**
   * Split text into sentences
   */
  private splitIntoSentences(text: string): string[] {
    const sentences: string[] = [];
    let lastIndex = 0;
    let match;

    while ((match = TextChunkingService.SENTENCE_ENDINGS.exec(text)) !== null) {
      const sentence = text.substring(lastIndex, match.index + match[0].length).trim();
      if (sentence.length > 0) {
        sentences.push(sentence);
      }
      lastIndex = match.index + match[0].length;
    }

    // Add remaining text as a sentence if it exists
    const remaining = text.substring(lastIndex).trim();
    if (remaining.length > 0) {
      sentences.push(remaining);
    }

    return sentences;
  }

  /**
   * Post-process chunks to ensure quality and consistency
   */
  private postProcessChunks(
    chunks: TextChunk[],
    config: ChunkingConfig,
    preprocessing: TextPreprocessingOptions
  ): TextChunk[] {
    const processedChunks: TextChunk[] = [];

    for (let index = 0; index < chunks.length; index++) {
      const chunk = chunks[index];
      let processedText = chunk.text;

      if (preprocessing.trimChunks) {
        processedText = processedText.trim();
      }

      // Skip chunks that are too small
      if (processedText.length < (config.minChunkSize || 0)) {
        continue;
      }

      // Truncate chunks that are too large
      if (config.maxChunkSize && processedText.length > config.maxChunkSize) {
        processedText = processedText.substring(0, config.maxChunkSize);
        logger.warn(`Chunk ${index} truncated from ${chunk.text.length} to ${config.maxChunkSize} characters`);
      }

      processedChunks.push({
        ...chunk,
        id: chunk.id || `chunk_${index}`,
        text: processedText,
        metadata: {
          ...chunk.metadata,
          chunkIndex: index,
          originalLength: chunk.text.length,
          processedLength: processedText.length
        }
      });
    }

    return processedChunks;
  }

  /**
   * Utility method to estimate token count (rough approximation)
   */
  public estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Utility method to get optimal chunk size for a given token limit
   */
  public getOptimalChunkSize(tokenLimit: number): number {
    // Convert token limit to character count (rough approximation)
    return Math.floor(tokenLimit * 3.5); // Leave some buffer
  }

  /**
   * Create document-specific chunking configuration
   */
  public createDocumentSpecificConfig(
    documentType: string,
    baseConfig: Partial<ChunkingConfig> = {}
  ): ChunkingConfig {
    const config = { ...TextChunkingService.DEFAULT_CONFIG, ...baseConfig };

    switch (documentType.toLowerCase()) {
      case 'pdf':
        return {
          ...config,
          strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
          respectWordBoundaries: true,
          preserveFormatting: true
        };

      case 'docx':
      case 'doc':
        return {
          ...config,
          strategy: ChunkingStrategy.PARAGRAPH,
          preserveFormatting: true
        };

      case 'txt':
      case 'text':
        return {
          ...config,
          strategy: ChunkingStrategy.PARAGRAPH,
          respectWordBoundaries: true
        };

      case 'md':
      case 'markdown':
        return {
          ...config,
          strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
          separators: ['\n## ', '\n### ', '\n\n', '\n', ' ', ''],
          preserveFormatting: true
        };

      case 'csv':
        return {
          ...config,
          strategy: ChunkingStrategy.CHARACTER,
          chunkSize: 2000, // Larger chunks for structured data
          respectWordBoundaries: false
        };

      case 'ppt':
      case 'pptx':
        return {
          ...config,
          strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
          separators: ['\n---\n', '\n\n', '\n', ' ', ''], // Slide separators
          chunkSize: 800 // Smaller chunks for presentation content
        };

      default:
        return config;
    }
  }
}
