import { logger } from '@/utils/logger';
import { TextChunk } from '@/types/document';

/**
 * Metadata Extraction Service
 * 
 * Provides comprehensive metadata extraction and preservation for text chunks
 * including source tracking, position information, and content analysis.
 */
export class MetadataExtractionService {
  private static instance: MetadataExtractionService;

  // Language detection patterns (simplified)
  private static readonly LANGUAGE_PATTERNS = {
    english: ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with', 'for', 'as', 'was', 'on', 'are'],
    spanish: ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su'],
    french: ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son'],
    german: ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich', 'des', 'auf', 'für', 'ist', 'im'],
    italian: ['il', 'di', 'che', 'e', 'la', 'per', 'in', 'un', 'è', 'con', 'non', 'da', 'su', 'del', 'al']
  };

  // Content type patterns
  private static readonly CONTENT_PATTERNS = {
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    url: /https?:\/\/[^\s]+/g,
    phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
    date: /\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b/g,
    time: /\b\d{1,2}:\d{2}(?::\d{2})?\s?(?:AM|PM|am|pm)?\b/g,
    number: /\b\d+(?:\.\d+)?\b/g,
    currency: /\$\d+(?:\.\d{2})?|\b\d+(?:\.\d{2})?\s?(?:USD|EUR|GBP|CAD|AUD)\b/g,
    percentage: /\b\d+(?:\.\d+)?%\b/g,
    code: /```[\s\S]*?```|`[^`]+`/g,
    heading: /^#{1,6}\s+.+$/gm,
    listItem: /^\s*[-*+]\s+.+$/gm,
    numberedList: /^\s*\d+\.\s+.+$/gm
  };

  public static getInstance(): MetadataExtractionService {
    if (!MetadataExtractionService.instance) {
      MetadataExtractionService.instance = new MetadataExtractionService();
    }
    return MetadataExtractionService.instance;
  }

  /**
   * Extract comprehensive metadata from a text chunk
   */
  public extractChunkMetadata(
    chunk: TextChunk,
    sourceMetadata: Record<string, any> = {},
    fullText?: string
  ): TextChunk {
    const metadata = {
      ...chunk.metadata,
      ...sourceMetadata,
      
      // Basic metrics
      characterCount: chunk.text.length,
      wordCount: this.countWords(chunk.text),
      sentenceCount: this.countSentences(chunk.text),
      paragraphCount: this.countParagraphs(chunk.text),
      
      // Position information
      relativePosition: fullText ? chunk.startIndex / fullText.length : 0,
      
      // Content analysis
      language: this.detectLanguage(chunk.text),
      contentType: this.analyzeContentType(chunk.text),
      complexity: this.analyzeComplexity(chunk.text),
      
      // Structural information
      hasHeadings: this.hasHeadings(chunk.text),
      hasList: this.hasList(chunk.text),
      hasCode: this.hasCode(chunk.text),
      hasUrls: this.hasUrls(chunk.text),
      hasEmails: this.hasEmails(chunk.text),
      
      // Quality metrics
      readability: this.calculateReadability(chunk.text),
      density: this.calculateInformationDensity(chunk.text),
      
      // Extraction timestamp
      extractedAt: new Date().toISOString(),
      
      // Chunk quality score
      qualityScore: this.calculateQualityScore(chunk.text)
    };

    return {
      ...chunk,
      metadata
    };
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Count sentences in text
   */
  private countSentences(text: string): number {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    return sentences.length;
  }

  /**
   * Count paragraphs in text
   */
  private countParagraphs(text: string): number {
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    return paragraphs.length;
  }

  /**
   * Detect the primary language of the text
   */
  private detectLanguage(text: string): string {
    const words = text.toLowerCase().split(/\s+/);
    const sample = words.slice(0, 100); // Use first 100 words for detection
    
    let bestLanguage = 'unknown';
    let maxMatches = 0;

    Object.entries(MetadataExtractionService.LANGUAGE_PATTERNS).forEach(([lang, patterns]) => {
      const matches = patterns.filter(pattern => 
        sample.some(word => word.includes(pattern))
      ).length;

      if (matches > maxMatches) {
        maxMatches = matches;
        bestLanguage = lang;
      }
    });

    return maxMatches > 2 ? bestLanguage : 'unknown';
  }

  /**
   * Analyze content type and extract relevant entities
   */
  private analyzeContentType(text: string): {
    type: string;
    entities: Record<string, number>;
    hasStructuredData: boolean;
  } {
    const entities: Record<string, number> = {};
    let hasStructuredData = false;

    // Count different types of entities
    Object.entries(MetadataExtractionService.CONTENT_PATTERNS).forEach(([type, pattern]) => {
      const matches = text.match(pattern);
      entities[type] = matches ? matches.length : 0;
      
      if (matches && matches.length > 0 && ['email', 'url', 'phone', 'date', 'currency'].includes(type)) {
        hasStructuredData = true;
      }
    });

    // Determine primary content type
    let primaryType = 'text';
    if (entities.code > 0) primaryType = 'code';
    else if (entities.heading > 2) primaryType = 'documentation';
    else if (entities.listItem > 3 || entities.numberedList > 3) primaryType = 'list';
    else if (hasStructuredData) primaryType = 'structured';

    return {
      type: primaryType,
      entities,
      hasStructuredData
    };
  }

  /**
   * Analyze text complexity
   */
  private analyzeComplexity(text: string): {
    averageWordsPerSentence: number;
    averageCharactersPerWord: number;
    uniqueWordRatio: number;
    complexityScore: number;
  } {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));

    const averageWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
    const averageCharactersPerWord = words.length > 0 ? 
      words.reduce((sum, word) => sum + word.length, 0) / words.length : 0;
    const uniqueWordRatio = words.length > 0 ? uniqueWords.size / words.length : 0;

    // Simple complexity score (0-100)
    const complexityScore = Math.min(100, 
      (averageWordsPerSentence * 2) + 
      (averageCharactersPerWord * 5) + 
      (uniqueWordRatio * 30)
    );

    return {
      averageWordsPerSentence,
      averageCharactersPerWord,
      uniqueWordRatio,
      complexityScore
    };
  }

  /**
   * Check if text has headings
   */
  private hasHeadings(text: string): boolean {
    return MetadataExtractionService.CONTENT_PATTERNS.heading.test(text);
  }

  /**
   * Check if text has lists
   */
  private hasList(text: string): boolean {
    return MetadataExtractionService.CONTENT_PATTERNS.listItem.test(text) ||
           MetadataExtractionService.CONTENT_PATTERNS.numberedList.test(text);
  }

  /**
   * Check if text has code
   */
  private hasCode(text: string): boolean {
    return MetadataExtractionService.CONTENT_PATTERNS.code.test(text);
  }

  /**
   * Check if text has URLs
   */
  private hasUrls(text: string): boolean {
    return MetadataExtractionService.CONTENT_PATTERNS.url.test(text);
  }

  /**
   * Check if text has email addresses
   */
  private hasEmails(text: string): boolean {
    return MetadataExtractionService.CONTENT_PATTERNS.email.test(text);
  }

  /**
   * Calculate readability score (simplified Flesch Reading Ease)
   */
  private calculateReadability(text: string): number {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    // Simplified Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Count syllables in a word (simplified)
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    // Handle silent 'e'
    if (word.endsWith('e')) {
      count--;
    }

    return Math.max(1, count);
  }

  /**
   * Calculate information density
   */
  private calculateInformationDensity(text: string): number {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));
    const entities = this.analyzeContentType(text).entities;
    
    const entityCount = Object.values(entities).reduce((sum, count) => sum + count, 0);
    const density = words.length > 0 ? 
      ((uniqueWords.size + entityCount) / words.length) * 100 : 0;
    
    return Math.min(100, density);
  }

  /**
   * Calculate overall quality score for the chunk
   */
  private calculateQualityScore(text: string): number {
    const length = text.length;
    const words = this.countWords(text);
    const readability = this.calculateReadability(text);
    const density = this.calculateInformationDensity(text);
    
    // Quality factors
    let score = 50; // Base score
    
    // Length factor (optimal range: 200-2000 characters)
    if (length >= 200 && length <= 2000) {
      score += 20;
    } else if (length < 50) {
      score -= 30;
    } else if (length > 5000) {
      score -= 10;
    }
    
    // Word count factor (optimal range: 50-500 words)
    if (words >= 50 && words <= 500) {
      score += 15;
    } else if (words < 10) {
      score -= 25;
    }
    
    // Readability factor
    if (readability >= 30 && readability <= 70) {
      score += 10;
    } else if (readability < 10 || readability > 90) {
      score -= 10;
    }
    
    // Information density factor
    if (density >= 20 && density <= 60) {
      score += 5;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Extract contextual relationships between chunks
   */
  public extractChunkRelationships(
    chunks: TextChunk[],
    fullText: string
  ): TextChunk[] {
    return chunks.map((chunk, index) => {
      const relationships = {
        previousChunk: index > 0 ? chunks[index - 1].id : null,
        nextChunk: index < chunks.length - 1 ? chunks[index + 1].id : null,
        semanticSimilarity: this.calculateSemanticSimilarity(chunk, chunks),
        topicalContinuity: this.calculateTopicalContinuity(chunk, chunks, index)
      };

      return {
        ...chunk,
        metadata: {
          ...chunk.metadata,
          relationships
        }
      };
    });
  }

  /**
   * Calculate semantic similarity with other chunks (simplified)
   */
  private calculateSemanticSimilarity(
    targetChunk: TextChunk,
    allChunks: TextChunk[]
  ): Array<{ chunkId: string; similarity: number }> {
    const targetWords = new Set(
      targetChunk.text.toLowerCase().split(/\s+/).filter(w => w.length > 3)
    );

    return allChunks
      .filter(chunk => chunk.id !== targetChunk.id)
      .map(chunk => {
        const chunkWords = new Set(
          chunk.text.toLowerCase().split(/\s+/).filter(w => w.length > 3)
        );
        
        const intersection = new Set([...targetWords].filter(x => chunkWords.has(x)));
        const union = new Set([...targetWords, ...chunkWords]);
        
        const similarity = union.size > 0 ? intersection.size / union.size : 0;
        
        return {
          chunkId: chunk.id,
          similarity: Math.round(similarity * 100) / 100
        };
      })
      .filter(item => item.similarity > 0.1)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 5); // Top 5 similar chunks
  }

  /**
   * Calculate topical continuity score
   */
  private calculateTopicalContinuity(
    chunk: TextChunk,
    allChunks: TextChunk[],
    index: number
  ): number {
    if (index === 0) return 1;
    
    const previousChunk = allChunks[index - 1];
    const similarity = this.calculateSemanticSimilarity(chunk, [previousChunk]);
    
    return similarity.length > 0 ? similarity[0].similarity : 0;
  }
}
