import fs from 'fs';
import path from 'path';
import { getTextExtractor } from 'office-text-extractor';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  PowerPointMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class PowerPointProcessor implements IDocumentProcessor {
  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
           mimeType === 'application/vnd.ms-powerpoint' ||
           mimeType.includes('powerpoint') ||
           mimeType.includes('presentation');
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.POWERPOINT;
  }

  /**
   * Process a PowerPoint file and extract text content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing PowerPoint file: ${filePath}`);

      // Read the PowerPoint file
      const buffer = fs.readFileSync(filePath);

      // Extract text using office-text-extractor
      const extractor = getTextExtractor();
      const extractedText = await extractor.extractText({ input: buffer, type: 'buffer' });

      // Clean and process the extracted text
      const text = this.cleanExtractedText(extractedText);

      // Extract metadata
      const metadata = await this.extractMetadata(filePath, text);

      // Create chunks if requested
      const chunks = options?.chunkSize ? 
        this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) : 
        undefined;

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`PowerPoint processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown PowerPoint processing error';
      logger.error(`PowerPoint processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Clean extracted text from PowerPoint
   */
  private cleanExtractedText(text: string): string {
    return text
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove slide separators
      .replace(/---+/g, '\n\n')
      // Clean up line breaks
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  }

  /**
   * Extract metadata from PowerPoint file
   */
  private async extractMetadata(filePath: string, text: string): Promise<PowerPointMetadata> {
    try {
      const stats = fs.statSync(filePath);

      // Analyze content for metadata
      const slideCount = this.estimateSlideCount(text);
      const hasImages = this.detectImages(text);
      const hasNotes = this.detectNotes(text);
      const hasAnimations = this.detectAnimations(text);

      const metadata: PowerPointMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        size: stats.size,
        type: DocumentType.POWERPOINT,
        slideCount,
        hasImages,
        hasNotes,
        hasAnimations,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract PowerPoint metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Estimate slide count from text content
   */
  private estimateSlideCount(text: string): number {
    // Look for slide indicators
    const slideIndicators = [
      /Slide\s+\d+/gi,
      /Page\s+\d+/gi,
      /^\d+\.\s+/gm, // Numbered items that might be slide titles
    ];

    let maxCount = 1; // At least one slide

    slideIndicators.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        maxCount = Math.max(maxCount, matches.length);
      }
    });

    // Estimate based on content length (rough heuristic)
    const estimatedByLength = Math.max(1, Math.floor(text.length / 500));

    return Math.min(maxCount, estimatedByLength);
  }

  /**
   * Detect if presentation has images
   */
  private detectImages(text: string): boolean {
    const imageIndicators = [
      /\[image\]/gi,
      /\[picture\]/gi,
      /\[graphic\]/gi,
      /image\s*\d+/gi,
      /figure\s*\d+/gi
    ];

    return imageIndicators.some(pattern => pattern.test(text));
  }

  /**
   * Detect if presentation has speaker notes
   */
  private detectNotes(text: string): boolean {
    const noteIndicators = [
      /notes?:/gi,
      /speaker\s+notes?/gi,
      /\[note\]/gi
    ];

    return noteIndicators.some(pattern => pattern.test(text));
  }

  /**
   * Detect if presentation has animations
   */
  private detectAnimations(text: string): boolean {
    const animationIndicators = [
      /animation/gi,
      /transition/gi,
      /effect/gi,
      /\[animated\]/gi
    ];

    return animationIndicators.some(pattern => pattern.test(text));
  }

  /**
   * Create text chunks from PowerPoint content
   */
  private createChunks(text: string, chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];

    // Try to split by slides first
    const slideTexts = this.splitBySlides(text);

    if (slideTexts.length > 1) {
      // Create chunks by slides
      slideTexts.forEach((slideText, index) => {
        if (slideText.trim()) {
          chunks.push({
            id: `ppt_slide_${index + 1}`,
            text: slideText.trim(),
            startIndex: 0, // Relative to slide
            endIndex: slideText.length,
            metadata: {
              source: 'powerpoint',
              slideNumber: index + 1,
              chunkIndex: chunks.length
            }
          });
        }
      });
    } else {
      // Fall back to text-based chunking
      let currentPos = 0;

      while (currentPos < text.length) {
        const endPos = Math.min(currentPos + chunkSize, text.length);
        const chunkText = text.substring(currentPos, endPos);

        chunks.push({
          id: `ppt_chunk_${chunks.length}`,
          text: chunkText,
          startIndex: currentPos,
          endIndex: endPos,
          metadata: {
            source: 'powerpoint',
            chunkIndex: chunks.length
          }
        });

        // Move to next chunk with overlap
        currentPos = endPos - overlap;
        if (currentPos >= text.length) break;
      }
    }

    return chunks;
  }

  /**
   * Split text by slides
   */
  private splitBySlides(text: string): string[] {
    // Try different slide separators
    const separators = [
      /\n\s*Slide\s+\d+/gi,
      /\n\s*Page\s+\d+/gi,
      /\n\s*\d+\.\s+/g, // Numbered sections
      /\n\s*---+\s*\n/g // Horizontal rules
    ];

    for (const separator of separators) {
      const parts = text.split(separator);
      if (parts.length > 1) {
        return parts.filter(part => part.trim().length > 0);
      }
    }

    // If no clear separators found, return as single slide
    return [text];
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): PowerPointMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      size: stats.size,
      type: DocumentType.POWERPOINT,
      slideCount: 1,
      hasImages: false,
      hasNotes: false,
      hasAnimations: false,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): PowerPointMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
