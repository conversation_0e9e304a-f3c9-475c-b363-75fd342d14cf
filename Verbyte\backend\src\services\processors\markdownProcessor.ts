import fs from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  MarkdownMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class MarkdownProcessor implements IDocumentProcessor {
  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'text/markdown' ||
           mimeType === 'text/x-markdown' ||
           mimeType.includes('markdown');
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.MARKDOWN;
  }

  /**
   * Process a Markdown file and extract text content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing Markdown file: ${filePath}`);

      // Read the markdown file
      const markdownContent = fs.readFileSync(filePath, 'utf-8');

      // Extract front matter if present
      const { content, frontMatter } = this.extractFrontMatter(markdownContent);

      // Extract metadata
      const metadata = await this.extractMetadata(filePath, content, frontMatter);

      // Convert markdown to plain text
      const text = options?.preserveFormatting ? content : this.markdownToText(content);

      // Create chunks if requested
      const chunks = options?.chunkSize ? 
        this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) : 
        undefined;

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`Markdown processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown Markdown processing error';
      logger.error(`Markdown processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Extract front matter from markdown content
   */
  private extractFrontMatter(content: string): { content: string; frontMatter?: Record<string, any> } {
    const frontMatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n/;
    const match = content.match(frontMatterRegex);

    if (!match) {
      return { content };
    }

    try {
      const frontMatterText = match[1];
      const frontMatter: Record<string, any> = {};

      // Simple YAML-like parsing
      frontMatterText.split('\n').forEach(line => {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
          const key = line.substring(0, colonIndex).trim();
          const value = line.substring(colonIndex + 1).trim();
          frontMatter[key] = value.replace(/^["']|["']$/g, ''); // Remove quotes
        }
      });

      return {
        content: content.replace(frontMatterRegex, ''),
        frontMatter
      };
    } catch (error) {
      logger.warn('Failed to parse front matter, treating as regular content');
      return { content };
    }
  }

  /**
   * Convert markdown to plain text
   */
  private markdownToText(markdown: string): string {
    return markdown
      // Remove code blocks
      .replace(/```[\s\S]*?```/g, '')
      .replace(/`[^`]*`/g, '')
      // Remove headers
      .replace(/^#{1,6}\s+/gm, '')
      // Remove links but keep text
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // Remove images
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
      // Remove bold/italic
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      .replace(/\*([^*]+)\*/g, '$1')
      .replace(/__([^_]+)__/g, '$1')
      .replace(/_([^_]+)_/g, '$1')
      // Remove strikethrough
      .replace(/~~([^~]+)~~/g, '$1')
      // Remove horizontal rules
      .replace(/^---+$/gm, '')
      // Remove list markers
      .replace(/^\s*[-*+]\s+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove blockquotes
      .replace(/^\s*>\s+/gm, '')
      // Clean up whitespace
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  }

  /**
   * Extract metadata from markdown content
   */
  private async extractMetadata(
    filePath: string, 
    content: string, 
    frontMatter?: Record<string, any>
  ): Promise<MarkdownMetadata> {
    try {
      const stats = fs.statSync(filePath);

      // Analyze content structure
      const hasHeaders = /^#{1,6}\s+/m.test(content);
      const hasLinks = /\[([^\]]+)\]\([^)]+\)/.test(content);
      const hasImages = /!\[([^\]]*)\]\([^)]+\)/.test(content);
      const hasTables = /\|.*\|/.test(content);
      const hasCodeBlocks = /```/.test(content);

      const metadata: MarkdownMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'text/markdown',
        size: stats.size,
        type: DocumentType.MARKDOWN,
        hasHeaders,
        hasLinks,
        hasImages,
        hasTables,
        hasCodeBlocks,
        frontMatter,
        title: frontMatter?.title || this.extractTitle(content),
        author: frontMatter?.author,
        createdAt: frontMatter?.date ? new Date(frontMatter.date) : stats.birthtime,
        modifiedAt: stats.mtime
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract Markdown metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Extract title from markdown content
   */
  private extractTitle(content: string): string | undefined {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1].trim() : undefined;
  }

  /**
   * Create text chunks from markdown content
   */
  private createChunks(text: string, chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];
    let currentPos = 0;

    while (currentPos < text.length) {
      const endPos = Math.min(currentPos + chunkSize, text.length);
      const chunkText = text.substring(currentPos, endPos);

      chunks.push({
        id: `markdown_chunk_${chunks.length}`,
        text: chunkText,
        startIndex: currentPos,
        endIndex: endPos,
        metadata: {
          source: 'markdown',
          chunkIndex: chunks.length
        }
      });

      // Move to next chunk with overlap
      currentPos = endPos - overlap;
      if (currentPos >= text.length) break;
    }

    return chunks;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): MarkdownMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'text/markdown',
      size: stats.size,
      type: DocumentType.MARKDOWN,
      hasHeaders: false,
      hasLinks: false,
      hasImages: false,
      hasTables: false,
      hasCodeBlocks: false,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): MarkdownMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
