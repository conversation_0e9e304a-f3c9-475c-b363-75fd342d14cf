import fs from 'fs';
import path from 'path';
import { DocumentProcessingService } from '@/services/documentProcessor';
import { IntegratedDocumentProcessingService } from '@/services/documentProcessingService';
import { UploadService } from '@/services/uploadService';
import { TextProcessor } from '@/services/processors/textProcessor';
import { JSONProcessor } from '@/services/processors/jsonProcessor';
import { MarkdownProcessor } from '@/services/processors/markdownProcessor';
import { DocumentType, ProcessingOptions } from '@/types/document';
import { UploadStatus, ProcessingStatus } from '@/models/Upload';

describe('DocumentProcessingService', () => {
  let documentProcessor: DocumentProcessingService;
  let testFilesDir: string;

  beforeAll(() => {
    documentProcessor = new DocumentProcessingService();
    testFilesDir = path.join(__dirname, 'test-files');

    // Create test files directory
    if (!fs.existsSync(testFilesDir)) {
      fs.mkdirSync(testFilesDir, { recursive: true });
    }
  });

  afterAll(() => {
    // Clean up test files
    if (fs.existsSync(testFilesDir)) {
      fs.rmSync(testFilesDir, { recursive: true, force: true });
    }
  });

  describe('Text Processing', () => {
    let testTextFile: string;

    beforeEach(() => {
      testTextFile = path.join(testFilesDir, 'test.txt');
      const testContent = 'This is a test document.\n\nIt has multiple paragraphs.\n\nAnd some more content for testing.';
      fs.writeFileSync(testTextFile, testContent, 'utf-8');
    });

    afterEach(() => {
      if (fs.existsSync(testTextFile)) {
        fs.unlinkSync(testTextFile);
      }
    });

    test('should process text file successfully', async () => {
      const result = await documentProcessor.processDocument(testTextFile);

      expect(result.success).toBe(true);
      expect(result.text).toContain('This is a test document');
      expect(result.metadata.type).toBe(DocumentType.TEXT);
      expect(result.metadata.wordCount).toBeGreaterThan(0);
      expect(result.metadata.characterCount).toBeGreaterThan(0);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should create chunks when requested', async () => {
      const options: ProcessingOptions = {
        chunkSize: 50,
        chunkOverlap: 10
      };

      const result = await documentProcessor.processDocument(testTextFile, options);

      expect(result.success).toBe(true);
      expect(result.chunks).toBeDefined();
      expect(result.chunks!.length).toBeGreaterThan(1);

      result.chunks!.forEach(chunk => {
        expect(chunk.id).toBeDefined();
        expect(chunk.text).toBeDefined();
        expect(chunk.startIndex).toBeGreaterThanOrEqual(0);
        expect(chunk.endIndex).toBeGreaterThan(chunk.startIndex);
      });
    });
  });

  describe('JSON Processing', () => {
    let testJsonFile: string;

    beforeEach(() => {
      testJsonFile = path.join(testFilesDir, 'test.json');
      const testData = {
        title: 'Test Document',
        description: 'This is a test JSON document',
        content: 'Some content here',
        metadata: {
          author: 'Test Author',
          created: '2024-01-01'
        },
        items: [
          { name: 'Item 1', value: 'Value 1' },
          { name: 'Item 2', value: 'Value 2' }
        ]
      };
      fs.writeFileSync(testJsonFile, JSON.stringify(testData, null, 2), 'utf-8');
    });

    afterEach(() => {
      if (fs.existsSync(testJsonFile)) {
        fs.unlinkSync(testJsonFile);
      }
    });

    test('should process JSON file successfully', async () => {
      const result = await documentProcessor.processDocument(testJsonFile);

      expect(result.success).toBe(true);
      expect(result.text).toContain('Test Document');
      expect(result.text).toContain('test JSON document');
      expect(result.metadata.type).toBe(DocumentType.JSON);
      expect(result.metadata.wordCount).toBeGreaterThan(0);
    });

    test('should preserve formatting when requested', async () => {
      const options: ProcessingOptions = {
        preserveFormatting: true
      };

      const result = await documentProcessor.processDocument(testJsonFile, options);

      expect(result.success).toBe(true);
      expect(result.text).toContain('{');
      expect(result.text).toContain('}');
      expect(result.text).toContain('"title"');
    });
  });

  describe('Markdown Processing', () => {
    let testMarkdownFile: string;

    beforeEach(() => {
      testMarkdownFile = path.join(testFilesDir, 'test.md');
      const testContent = `---
title: Test Document
author: Test Author
---

# Main Title

This is a **bold** text and this is *italic*.

## Section 1

Some content here with a [link](https://example.com).

### Subsection

- List item 1
- List item 2
- List item 3

\`\`\`javascript
console.log('Hello World');
\`\`\`

> This is a blockquote.
`;
      fs.writeFileSync(testMarkdownFile, testContent, 'utf-8');
    });

    afterEach(() => {
      if (fs.existsSync(testMarkdownFile)) {
        fs.unlinkSync(testMarkdownFile);
      }
    });

    test('should process markdown file successfully', async () => {
      const result = await documentProcessor.processDocument(testMarkdownFile);

      expect(result.success).toBe(true);
      expect(result.text).toContain('Main Title');
      expect(result.text).toContain('bold text');
      expect(result.text).toContain('italic');
      expect(result.metadata.type).toBe(DocumentType.MARKDOWN);
      expect(result.metadata.title).toBe('Test Document');
      expect(result.metadata.author).toBe('Test Author');
    });

    test('should extract front matter', async () => {
      const result = await documentProcessor.processDocument(testMarkdownFile);

      expect(result.success).toBe(true);
      expect(result.metadata.frontMatter).toBeDefined();
      expect(result.metadata.frontMatter!.title).toBe('Test Document');
      expect(result.metadata.frontMatter!.author).toBe('Test Author');
    });
  });

  describe('File Type Detection', () => {
    test('should detect supported file types', () => {
      expect(documentProcessor.isSupported('test.txt')).toBe(true);
      expect(documentProcessor.isSupported('test.json')).toBe(true);
      expect(documentProcessor.isSupported('test.md')).toBe(true);
      expect(documentProcessor.isSupported('test.pdf')).toBe(true);
      expect(documentProcessor.isSupported('test.docx')).toBe(true);
      expect(documentProcessor.isSupported('test.csv')).toBe(true);
      expect(documentProcessor.isSupported('test.jpg')).toBe(true);
      expect(documentProcessor.isSupported('test.pptx')).toBe(true);
    });

    test('should reject unsupported file types', () => {
      expect(documentProcessor.isSupported('test.xyz')).toBe(false);
      expect(documentProcessor.isSupported('test.exe')).toBe(false);
      expect(documentProcessor.isSupported('test.bin')).toBe(false);
    });

    test('should return supported extensions', () => {
      const extensions = documentProcessor.getSupportedExtensions();

      expect(extensions).toContain('.txt');
      expect(extensions).toContain('.json');
      expect(extensions).toContain('.md');
      expect(extensions).toContain('.pdf');
      expect(extensions).toContain('.docx');
      expect(extensions).toContain('.csv');
      expect(extensions).toContain('.jpg');
      expect(extensions).toContain('.pptx');
    });
  });

  describe('Error Handling', () => {
    test('should handle non-existent files gracefully', async () => {
      const nonExistentFile = path.join(testFilesDir, 'non-existent.txt');

      const result = await documentProcessor.processDocument(nonExistentFile);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.text).toBe('');
    });

    test('should handle corrupted files gracefully', async () => {
      const corruptedFile = path.join(testFilesDir, 'corrupted.json');
      fs.writeFileSync(corruptedFile, '{ invalid json content', 'utf-8');

      const result = await documentProcessor.processDocument(corruptedFile);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.text).toBe('');

      // Clean up
      fs.unlinkSync(corruptedFile);
    });
  });

  describe('Batch Processing', () => {
    let testFiles: string[];

    beforeEach(() => {
      testFiles = [
        path.join(testFilesDir, 'batch1.txt'),
        path.join(testFilesDir, 'batch2.json'),
        path.join(testFilesDir, 'batch3.md')
      ];

      fs.writeFileSync(testFiles[0], 'Test content 1', 'utf-8');
      fs.writeFileSync(testFiles[1], '{"title": "Test 2"}', 'utf-8');
      fs.writeFileSync(testFiles[2], '# Test 3\n\nContent here', 'utf-8');
    });

    afterEach(() => {
      testFiles.forEach(file => {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
        }
      });
    });

    test('should process multiple files in batch', async () => {
      const result = await documentProcessor.processBatch(testFiles);

      expect(result.totalFiles).toBe(3);
      expect(result.successfulFiles).toBe(3);
      expect(result.failedFiles).toBe(0);
      expect(result.results).toHaveLength(3);
      expect(result.processingTime).toBeGreaterThan(0);

      result.results.forEach(fileResult => {
        expect(fileResult.success).toBe(true);
        expect(fileResult.text).toBeDefined();
        expect(fileResult.metadata).toBeDefined();
      });
    });
  });
});

describe('IntegratedDocumentProcessingService', () => {
  let integratedService: IntegratedDocumentProcessingService;
  let uploadService: UploadService;
  let testFilesDir: string;

  beforeAll(() => {
    integratedService = new IntegratedDocumentProcessingService();
    uploadService = new UploadService();
    testFilesDir = path.join(__dirname, 'test-files-integrated');

    // Create test files directory
    if (!fs.existsSync(testFilesDir)) {
      fs.mkdirSync(testFilesDir, { recursive: true });
    }
  });

  afterAll(() => {
    // Clean up test files
    if (fs.existsSync(testFilesDir)) {
      fs.rmSync(testFilesDir, { recursive: true, force: true });
    }
  });

  describe('Upload Integration', () => {
    let testFile: string;
    let uploadId: string;

    beforeEach(async () => {
      testFile = path.join(testFilesDir, 'integration-test.txt');
      const testContent = 'This is a test file for integration testing.';
      fs.writeFileSync(testFile, testContent, 'utf-8');

      // Create upload record
      const upload = await uploadService.createUpload({
        filename: 'integration-test.txt',
        originalName: 'integration-test.txt',
        mimeType: 'text/plain',
        size: Buffer.byteLength(testContent, 'utf-8'),
        path: testFile
      });

      uploadId = upload._id.toString();
    });

    afterEach(async () => {
      // Clean up upload and file
      try {
        await uploadService.deleteUpload(uploadId);
      } catch (error) {
        // Upload might already be deleted
      }

      if (fs.existsSync(testFile)) {
        fs.unlinkSync(testFile);
      }
    });

    test('should process uploaded document and update status', async () => {
      const result = await integratedService.processUploadedDocument(uploadId);

      expect(result.success).toBe(true);
      expect(result.text).toContain('integration testing');

      // Check that upload status was updated
      const upload = await uploadService.getUploadById(uploadId);
      expect(upload).toBeTruthy();
      expect(upload!.status).toBe(UploadStatus.COMPLETED);
      expect(upload!.processingStatus).toBe(ProcessingStatus.COMPLETED);
      expect(upload!.uploadProgress).toBe(100);
      expect(upload!.processingProgress).toBe(100);
      expect(upload!.metadata.extractedText).toBe(true);
    });

    test('should handle processing errors gracefully', async () => {
      // Delete the file to cause an error
      fs.unlinkSync(testFile);

      const result = await integratedService.processUploadedDocument(uploadId);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();

      // Check that upload status was updated to failed
      const upload = await uploadService.getUploadById(uploadId);
      expect(upload).toBeTruthy();
      expect(upload!.status).toBe(UploadStatus.FAILED);
      expect(upload!.processingStatus).toBe(ProcessingStatus.FAILED);
      expect(upload!.error).toBeDefined();
    });
  });

  describe('File Type Support', () => {
    test('should check file support correctly', () => {
      expect(integratedService.isFileSupported('test.txt')).toBe(true);
      expect(integratedService.isFileSupported('test.json')).toBe(true);
      expect(integratedService.isFileSupported('test.xyz')).toBe(false);
    });

    test('should return supported extensions', () => {
      const extensions = integratedService.getSupportedExtensions();
      expect(Array.isArray(extensions)).toBe(true);
      expect(extensions.length).toBeGreaterThan(0);
      expect(extensions).toContain('.txt');
    });
  });
});
