/**
 * Embedding Service - Main service for generating embeddings
 * Supports HuggingFace, OpenAI, and Ollama providers
 */

import { config } from '../config/environment';
import { logger } from '../utils/logger';
import { 
  EmbeddingProvider, 
  EmbeddingServiceType, 
  Embedding, 
  EmbeddingResponse,
  EmbeddingCacheEntry 
} from '../types/document';
import { HuggingFaceEmbeddingProvider } from './embeddings/huggingfaceProvider';
import { OpenAIEmbeddingProvider } from './embeddings/openaiProvider';
import { OllamaEmbeddingProvider } from './embeddings/ollamaProvider';
import { EmbeddingCache } from './embeddings/embeddingCache';

export class EmbeddingService {
  private provider: EmbeddingProvider;
  private cache: EmbeddingCache;
  private readonly serviceType: EmbeddingServiceType;

  constructor() {
    this.serviceType = config.embedding.service as EmbeddingServiceType;
    this.cache = new EmbeddingCache();
    this.provider = this.createProvider();
    
    logger.info(`Initialized embedding service with provider: ${this.serviceType}`);
  }

  /**
   * Create the appropriate embedding provider based on configuration
   */
  private createProvider(): EmbeddingProvider {
    switch (this.serviceType) {
      case 'huggingface':
        return new HuggingFaceEmbeddingProvider();
      case 'openai':
        return new OpenAIEmbeddingProvider();
      case 'ollama':
        return new OllamaEmbeddingProvider();
      default:
        throw new Error(`Unsupported embedding service: ${this.serviceType}`);
    }
  }

  /**
   * Generate embeddings for a list of texts
   * This is the main method that matches the Python API
   */
  async textsEmbeddings(texts: string[]): Promise<Embedding[]> {
    if (!texts || texts.length === 0) {
      throw new Error('No texts provided for embedding generation');
    }

    logger.debug(`Generating embeddings for ${texts.length} texts using ${this.serviceType}`);

    try {
      // Check cache first
      const cachedResults: (number[] | null)[] = await Promise.all(
        texts.map(text => this.cache.get(text, this.provider.getModelName()))
      );

      const uncachedTexts: string[] = [];
      const uncachedIndices: number[] = [];

      // Identify texts that need embedding generation
      texts.forEach((text, index) => {
        if (cachedResults[index] === null) {
          uncachedTexts.push(text);
          uncachedIndices.push(index);
        }
      });

      let newEmbeddings: number[][] = [];
      if (uncachedTexts.length > 0) {
        logger.debug(`Generating ${uncachedTexts.length} new embeddings`);
        newEmbeddings = await this.provider.generateEmbeddings(uncachedTexts);
        
        // Cache the new embeddings
        await Promise.all(
          uncachedTexts.map((text, i) => 
            this.cache.set(text, newEmbeddings[i], this.provider.getModelName(), this.provider.getDimension())
          )
        );
      }

      // Combine cached and new embeddings
      const allEmbeddings: number[][] = new Array(texts.length);
      let newEmbeddingIndex = 0;

      for (let i = 0; i < texts.length; i++) {
        if (cachedResults[i] !== null) {
          allEmbeddings[i] = cachedResults[i]!;
        } else {
          allEmbeddings[i] = newEmbeddings[newEmbeddingIndex++];
        }
      }

      // Convert to the expected format
      const embeddings: Embedding[] = allEmbeddings.map((embedding, index) => ({
        index,
        object: 'embedding' as const,
        embedding
      }));

      logger.debug(`Successfully generated ${embeddings.length} embeddings`);
      return embeddings;

    } catch (error) {
      logger.error('Error generating embeddings:', error);
      throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embedding for a single text
   */
  async textEmbedding(text: string): Promise<Embedding> {
    const embeddings = await this.textsEmbeddings([text]);
    return embeddings[0];
  }

  /**
   * Create an embedding response in the format expected by the API
   */
  async createEmbeddingResponse(input: string | string[]): Promise<EmbeddingResponse> {
    const inputTexts = Array.isArray(input) ? input : [input];
    const embeddings = await this.textsEmbeddings(inputTexts);

    return {
      object: 'list',
      model: this.provider.getModelName(),
      data: embeddings,
      usage: {
        prompt_tokens: this.calculateTokens(inputTexts),
        total_tokens: this.calculateTokens(inputTexts)
      }
    };
  }

  /**
   * Get the dimension of embeddings from the current provider
   */
  getDimension(): number {
    return this.provider.getDimension();
  }

  /**
   * Get the model name from the current provider
   */
  getModelName(): string {
    return this.provider.getModelName();
  }

  /**
   * Get the current service type
   */
  getServiceType(): EmbeddingServiceType {
    return this.serviceType;
  }

  /**
   * Process embeddings in batches for better performance
   */
  async batchEmbeddings(texts: string[], batchSize: number = 10): Promise<Embedding[]> {
    if (texts.length <= batchSize) {
      return this.textsEmbeddings(texts);
    }

    const batches: string[][] = [];
    for (let i = 0; i < texts.length; i += batchSize) {
      batches.push(texts.slice(i, i + batchSize));
    }

    logger.debug(`Processing ${texts.length} texts in ${batches.length} batches of size ${batchSize}`);

    const allEmbeddings: Embedding[] = [];
    for (const batch of batches) {
      const batchEmbeddings = await this.textsEmbeddings(batch);
      // Adjust indices to be global
      batchEmbeddings.forEach(embedding => {
        embedding.index = allEmbeddings.length;
        allEmbeddings.push(embedding);
      });
    }

    return allEmbeddings;
  }

  /**
   * Simple token calculation (approximation)
   */
  private calculateTokens(texts: string[]): number {
    return texts.reduce((total, text) => total + Math.ceil(text.length / 4), 0);
  }

  /**
   * Clear the embedding cache
   */
  async clearCache(): Promise<void> {
    await this.cache.clear();
    logger.info('Embedding cache cleared');
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{ hits: number; misses: number; size: number }> {
    return this.cache.getStats();
  }
}

// Singleton instance
let embeddingServiceInstance: EmbeddingService | null = null;

export function getEmbeddingService(): EmbeddingService {
  if (!embeddingServiceInstance) {
    embeddingServiceInstance = new EmbeddingService();
  }
  return embeddingServiceInstance;
}
