import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { config } from '@/config/environment';
import { connectDatabase } from '@/config/database';
import { connectQdrant } from '@/config/qdrant';
import { fileCleanupService } from '@/utils/fileCleanup';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { authRoutes } from '@/routes/auth';
import { healthRoutes } from '@/routes/health';
import { ingestRoutes } from '@/routes/ingest';
import { chatRoutes } from '@/routes/chat';
import { chunksRoutes } from '@/routes/chunks';
import { embeddingsRoutes } from '@/routes/embeddings';
import chunkingRoutes from '@/routes/chunking';

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
app.use(cors({
  origin: config.cors.origin,
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Logging middleware
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim()),
  },
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API routes
app.use(`${config.apiPrefix}/auth`, authRoutes);
app.use(`${config.apiPrefix}/health`, healthRoutes);
app.use(`${config.apiPrefix}/ingest`, ingestRoutes);
app.use(`${config.apiPrefix}/chat`, chatRoutes);
app.use(`${config.apiPrefix}/chunks`, chunksRoutes);
app.use(`${config.apiPrefix}/embeddings`, embeddingsRoutes);
app.use(`${config.apiPrefix}/chunking`, chunkingRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Graceful shutdown handler
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  // Stop cleanup service
  fileCleanupService.stopCleanupJob();

  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
const startServer = async () => {
  try {
    // Connect to databases
    await connectDatabase();
    await connectQdrant();

    // Start file cleanup service
    fileCleanupService.startCleanupJob();

    // Start HTTP server
    const server = app.listen(config.port, () => {
      logger.info(`🚀 Verbyte server running on port ${config.port}`);
      logger.info(`📚 API documentation available at http://localhost:${config.port}${config.apiPrefix}/docs`);
      logger.info(`🏥 Health check available at http://localhost:${config.port}${config.apiPrefix}/health`);
      logger.info(`🧹 File cleanup service started`);
    });

    // Handle server errors
    server.on('error', (error: Error) => {
      logger.error('Server error:', error);
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the application
startServer();

export default app;
