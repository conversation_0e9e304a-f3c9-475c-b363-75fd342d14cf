/**
 * Tests for Embedding Service
 */

import { EmbeddingService, getEmbeddingService } from '../services/embeddingService';
import { HuggingFaceEmbeddingProvider } from '../services/embeddings/huggingfaceProvider';
import { OpenAIEmbeddingProvider } from '../services/embeddings/openaiProvider';
import { OllamaEmbeddingProvider } from '../services/embeddings/ollamaProvider';
import { EmbeddingCache } from '../services/embeddings/embeddingCache';

// Mock the config
jest.mock('../config/environment', () => ({
  config: {
    env: 'test',
    port: 8001,
    apiPrefix: '/v1',
    database: {
      uri: 'mongodb://localhost:27017/verbyte_test',
      testUri: 'mongodb://localhost:27017/verbyte_test'
    },
    qdrant: {
      url: 'http://localhost:6333',
      apiKey: '',
      collectionName: 'verbyte_documents_test'
    },
    auth: {
      jwtSecret: 'test-secret',
      jwtExpiresIn: '7d',
      bcryptRounds: 12
    },
    upload: {
      maxFileSize: 10485760,
      uploadDir: 'uploads',
      allowedFileTypes: ['pdf', 'docx', 'txt']
    },
    embedding: {
      service: 'huggingface',
      model: 'sentence-transformers/all-MiniLM-L6-v2',
      dimension: 384
    },
    huggingface: {
      apiKey: 'test-hf-key',
      apiUrl: 'https://api-inference.huggingface.co/pipeline/feature-extraction'
    },
    openai: {
      apiKey: 'test-openai-key',
      embeddingModel: 'text-embedding-ada-002',
      chatModel: 'gpt-3.5-turbo'
    },
    ollama: {
      baseUrl: 'http://localhost:11434',
      embeddingModel: 'nomic-embed-text',
      chatModel: 'llama3.1'
    },
    llm: {
      service: 'openai',
      maxTokens: 512,
      temperature: 0.1,
      contextWindow: 3900
    },
    rag: {
      similarityTopK: 5,
      similarityThreshold: 0.7,
      chunkSize: 512,
      chunkOverlap: 50,
      rerankEnabled: false,
      rerankModel: 'cross-encoder/ms-marco-MiniLM-L-2-v2',
      rerankTopN: 3
    },
    rateLimiting: {
      windowMs: 900000,
      maxRequests: 100
    }
  }
}));

// Mock the logger
jest.mock('../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

// Mock the providers
jest.mock('../services/embeddings/huggingfaceProvider');
jest.mock('../services/embeddings/openaiProvider');
jest.mock('../services/embeddings/ollamaProvider');
jest.mock('../services/embeddings/embeddingCache');

describe('EmbeddingService', () => {
  let embeddingService: EmbeddingService;
  let mockProvider: jest.Mocked<HuggingFaceEmbeddingProvider>;
  let mockCache: jest.Mocked<EmbeddingCache>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock provider
    mockProvider = {
      generateEmbeddings: jest.fn(),
      getDimension: jest.fn().mockReturnValue(384),
      getModelName: jest.fn().mockReturnValue('test-model')
    } as any;

    // Setup mock cache
    mockCache = {
      get: jest.fn(),
      set: jest.fn(),
      clear: jest.fn(),
      getStats: jest.fn().mockResolvedValue({ hits: 0, misses: 0, size: 0 })
    } as any;

    // Mock the constructor to return our mocks
    (HuggingFaceEmbeddingProvider as jest.MockedClass<typeof HuggingFaceEmbeddingProvider>)
      .mockImplementation(() => mockProvider);

    (EmbeddingCache as jest.MockedClass<typeof EmbeddingCache>)
      .mockImplementation(() => mockCache);

    embeddingService = new EmbeddingService();
  });

  describe('textsEmbeddings', () => {
    it('should generate embeddings for single text', async () => {
      const testText = 'Hello world';
      const mockEmbedding = [0.1, 0.2, 0.3];

      mockCache.get.mockResolvedValue(null); // Cache miss
      mockProvider.generateEmbeddings.mockResolvedValue([mockEmbedding]);

      const result = await embeddingService.textsEmbeddings([testText]);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        index: 0,
        object: 'embedding',
        embedding: mockEmbedding
      });
      expect(mockProvider.generateEmbeddings).toHaveBeenCalledWith([testText]);
      expect(mockCache.set).toHaveBeenCalledWith(testText, mockEmbedding, 'test-model', 384);
    });

    it('should generate embeddings for multiple texts', async () => {
      const testTexts = ['Hello world', 'Goodbye world'];
      const mockEmbeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]];

      mockCache.get.mockResolvedValue(null); // Cache miss for all
      mockProvider.generateEmbeddings.mockResolvedValue(mockEmbeddings);

      const result = await embeddingService.textsEmbeddings(testTexts);

      expect(result).toHaveLength(2);
      expect(result[0].embedding).toEqual(mockEmbeddings[0]);
      expect(result[1].embedding).toEqual(mockEmbeddings[1]);
      expect(result[0].index).toBe(0);
      expect(result[1].index).toBe(1);
    });

    it('should use cached embeddings when available', async () => {
      const testText = 'Hello world';
      const cachedEmbedding = [0.1, 0.2, 0.3];

      mockCache.get.mockResolvedValue(cachedEmbedding); // Cache hit

      const result = await embeddingService.textsEmbeddings([testText]);

      expect(result).toHaveLength(1);
      expect(result[0].embedding).toEqual(cachedEmbedding);
      expect(mockProvider.generateEmbeddings).not.toHaveBeenCalled();
      expect(mockCache.set).not.toHaveBeenCalled();
    });

    it('should handle mixed cache hits and misses', async () => {
      const testTexts = ['Cached text', 'New text'];
      const cachedEmbedding = [0.1, 0.2, 0.3];
      const newEmbedding = [0.4, 0.5, 0.6];

      mockCache.get
        .mockResolvedValueOnce(cachedEmbedding) // Cache hit for first text
        .mockResolvedValueOnce(null); // Cache miss for second text

      mockProvider.generateEmbeddings.mockResolvedValue([newEmbedding]);

      const result = await embeddingService.textsEmbeddings(testTexts);

      expect(result).toHaveLength(2);
      expect(result[0].embedding).toEqual(cachedEmbedding);
      expect(result[1].embedding).toEqual(newEmbedding);
      expect(mockProvider.generateEmbeddings).toHaveBeenCalledWith(['New text']);
    });

    it('should throw error for empty input', async () => {
      await expect(embeddingService.textsEmbeddings([])).rejects.toThrow(
        'No texts provided for embedding generation'
      );
    });

    it('should handle provider errors', async () => {
      const testText = 'Hello world';
      const error = new Error('Provider error');

      mockCache.get.mockResolvedValue(null);
      mockProvider.generateEmbeddings.mockRejectedValue(error);

      await expect(embeddingService.textsEmbeddings([testText])).rejects.toThrow(
        'Failed to generate embeddings: Provider error'
      );
    });
  });

  describe('textEmbedding', () => {
    it('should generate embedding for single text', async () => {
      const testText = 'Hello world';
      const mockEmbedding = [0.1, 0.2, 0.3];

      mockCache.get.mockResolvedValue(null);
      mockProvider.generateEmbeddings.mockResolvedValue([mockEmbedding]);

      const result = await embeddingService.textEmbedding(testText);

      expect(result).toEqual({
        index: 0,
        object: 'embedding',
        embedding: mockEmbedding
      });
    });
  });

  describe('createEmbeddingResponse', () => {
    it('should create response for string input', async () => {
      const testText = 'Hello world';
      const mockEmbedding = [0.1, 0.2, 0.3];

      mockCache.get.mockResolvedValue(null);
      mockProvider.generateEmbeddings.mockResolvedValue([mockEmbedding]);

      const result = await embeddingService.createEmbeddingResponse(testText);

      expect(result).toEqual({
        object: 'list',
        model: 'test-model',
        data: [{
          index: 0,
          object: 'embedding',
          embedding: mockEmbedding
        }],
        usage: {
          prompt_tokens: 3, // "Hello world" ≈ 3 tokens
          total_tokens: 3
        }
      });
    });

    it('should create response for array input', async () => {
      const testTexts = ['Hello', 'world'];
      const mockEmbeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]];

      mockCache.get.mockResolvedValue(null);
      mockProvider.generateEmbeddings.mockResolvedValue(mockEmbeddings);

      const result = await embeddingService.createEmbeddingResponse(testTexts);

      expect(result.data).toHaveLength(2);
      expect(result.usage?.total_tokens).toBe(4); // "Hello" + "world" ≈ 4 tokens
    });
  });

  describe('batchEmbeddings', () => {
    it('should process small batches normally', async () => {
      const testTexts = ['Hello', 'world'];
      const mockEmbeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]];

      mockCache.get.mockResolvedValue(null);
      mockProvider.generateEmbeddings.mockResolvedValue(mockEmbeddings);

      const result = await embeddingService.batchEmbeddings(testTexts, 5);

      expect(result).toHaveLength(2);
      expect(mockProvider.generateEmbeddings).toHaveBeenCalledTimes(1);
    });

    it('should split large batches', async () => {
      const testTexts = ['Text1', 'Text2', 'Text3'];
      const mockEmbeddings1 = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]];
      const mockEmbeddings2 = [[0.7, 0.8, 0.9]];

      mockCache.get.mockResolvedValue(null);
      mockProvider.generateEmbeddings
        .mockResolvedValueOnce(mockEmbeddings1)
        .mockResolvedValueOnce(mockEmbeddings2);

      const result = await embeddingService.batchEmbeddings(testTexts, 2);

      expect(result).toHaveLength(3);
      expect(result[0].index).toBe(0);
      expect(result[1].index).toBe(1);
      expect(result[2].index).toBe(2);
      expect(mockProvider.generateEmbeddings).toHaveBeenCalledTimes(2);
    });
  });

  describe('utility methods', () => {
    it('should return correct dimension', () => {
      expect(embeddingService.getDimension()).toBe(384);
    });

    it('should return correct model name', () => {
      expect(embeddingService.getModelName()).toBe('test-model');
    });

    it('should return correct service type', () => {
      expect(embeddingService.getServiceType()).toBe('huggingface');
    });

    it('should clear cache', async () => {
      await embeddingService.clearCache();
      expect(mockCache.clear).toHaveBeenCalled();
    });

    it('should get cache stats', async () => {
      const stats = await embeddingService.getCacheStats();
      expect(stats).toEqual({ hits: 0, misses: 0, size: 0 });
      expect(mockCache.getStats).toHaveBeenCalled();
    });
  });
});

describe('getEmbeddingService singleton', () => {
  it('should return the same instance', () => {
    const service1 = getEmbeddingService();
    const service2 = getEmbeddingService();
    expect(service1).toBe(service2);
  });
});

describe('Provider Selection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create HuggingFace provider when configured', () => {
    // The provider is created in the constructor, so we check if it was called during setup
    expect(HuggingFaceEmbeddingProvider).toHaveBeenCalledTimes(1);
  });

  it('should handle provider creation', () => {
    // Test that the service was created successfully
    const service = new EmbeddingService();
    expect(service).toBeDefined();
    expect(service.getServiceType()).toBe('huggingface');
  });
});
