import { describe, it, expect } from '@jest/globals';

// Simple test without external dependencies
describe('Text Chunking - Simple Tests', () => {
  describe('Basic String Operations', () => {
    it('should split text by separators', () => {
      const text = 'First paragraph.\n\nSecond paragraph.\n\nThird paragraph.';
      const paragraphs = text.split(/\n\s*\n/);
      
      expect(paragraphs).toHaveLength(3);
      expect(paragraphs[0]).toBe('First paragraph.');
      expect(paragraphs[1]).toBe('Second paragraph.');
      expect(paragraphs[2]).toBe('Third paragraph.');
    });

    it('should handle sentence splitting', () => {
      const text = 'First sentence. Second sentence! Third sentence?';
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      
      expect(sentences).toHaveLength(3);
      expect(sentences[0].trim()).toBe('First sentence');
      expect(sentences[1].trim()).toBe(' Second sentence');
      expect(sentences[2].trim()).toBe(' Third sentence');
    });

    it('should normalize whitespace', () => {
      const text = 'Text   with    multiple     spaces\t\tand\ttabs\n\n\n\nand newlines.';
      
      // Normalize spaces and tabs
      const normalized = text.replace(/[ \t]+/g, ' ');
      expect(normalized).not.toContain('   ');
      expect(normalized).not.toContain('\t');
      
      // Remove excessive newlines
      const cleanNewlines = normalized.replace(/\n\s*\n\s*\n/g, '\n\n');
      expect(cleanNewlines).not.toContain('\n\n\n');
    });

    it('should handle character chunking logic', () => {
      const text = 'This is a test text that should be split into chunks.';
      const chunkSize = 20;
      const overlap = 5;
      
      const chunks = [];
      let currentPos = 0;
      
      while (currentPos < text.length) {
        const endPos = Math.min(currentPos + chunkSize, text.length);
        const chunkText = text.substring(currentPos, endPos);
        
        chunks.push({
          text: chunkText,
          start: currentPos,
          end: currentPos + chunkText.length
        });
        
        currentPos = currentPos + chunkText.length - overlap;
        if (currentPos >= text.length) break;
      }
      
      expect(chunks.length).toBeGreaterThan(1);
      expect(chunks[0].text.length).toBeLessThanOrEqual(chunkSize);
      
      // Check overlap
      if (chunks.length > 1) {
        const firstChunkEnd = chunks[0].text;
        const secondChunkStart = chunks[1].text;
        const overlapText = firstChunkEnd.slice(-overlap);
        expect(secondChunkStart.startsWith(overlapText)).toBe(true);
      }
    });

    it('should handle word boundary respect', () => {
      const text = 'This is a test text with some longer words that should not be broken.';
      const chunkSize = 25;
      
      let endPos = Math.min(chunkSize, text.length);
      let chunkText = text.substring(0, endPos);
      
      // Respect word boundaries if enabled
      if (endPos < text.length) {
        const lastSpaceIndex = chunkText.lastIndexOf(' ');
        if (lastSpaceIndex > chunkSize * 0.8) {
          chunkText = chunkText.substring(0, lastSpaceIndex);
          endPos = lastSpaceIndex;
        }
      }
      
      // Check that we don't end in the middle of a word
      if (endPos < text.length) {
        const nextChar = text[endPos];
        expect(nextChar).toMatch(/\s/);
      }
    });

    it('should handle recursive splitting logic', () => {
      const text = 'Paragraph 1.\n\nParagraph 2 with multiple sentences. This is another sentence.\n\nParagraph 3.';
      const separators = ['\n\n', '\n', '. ', ' ', ''];
      const maxChunkSize = 40;
      
      function splitRecursively(text: string, separators: string[], maxSize: number): string[] {
        if (text.length <= maxSize) {
          return text.trim().length > 0 ? [text.trim()] : [];
        }
        
        const separator = separators[0];
        if (!separator) {
          // Force split by character
          return [text.substring(0, maxSize)];
        }
        
        const splits = text.split(separator);
        if (splits.length === 1) {
          // Try next separator
          return splitRecursively(text, separators.slice(1), maxSize);
        }
        
        const result = [];
        let currentChunk = '';
        
        for (const split of splits) {
          const potential = currentChunk + (currentChunk ? separator : '') + split;
          if (potential.length <= maxSize) {
            currentChunk = potential;
          } else {
            if (currentChunk) {
              result.push(currentChunk);
            }
            currentChunk = split;
          }
        }
        
        if (currentChunk) {
          result.push(currentChunk);
        }
        
        return result;
      }
      
      const chunks = splitRecursively(text, separators, maxChunkSize);
      
      expect(chunks.length).toBeGreaterThan(1);
      chunks.forEach(chunk => {
        expect(chunk.length).toBeLessThanOrEqual(maxChunkSize);
      });
    });

    it('should calculate text metrics correctly', () => {
      const text = 'This is a test sentence. This is another sentence.';
      
      // Word count
      const words = text.trim().split(/\s+/).filter(word => word.length > 0);
      expect(words.length).toBe(10);
      
      // Sentence count
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      expect(sentences.length).toBe(2);
      
      // Character count
      expect(text.length).toBe(50);
      
      // Average words per sentence
      const avgWordsPerSentence = words.length / sentences.length;
      expect(avgWordsPerSentence).toBe(5);
    });

    it('should detect content patterns', () => {
      const text = 'Contact <NAME_EMAIL> or visit https://example.com for more info. Call ************.';
      
      // Email detection
      const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
      const emails = text.match(emailPattern);
      expect(emails).toHaveLength(1);
      expect(emails![0]).toBe('<EMAIL>');
      
      // URL detection
      const urlPattern = /https?:\/\/[^\s]+/g;
      const urls = text.match(urlPattern);
      expect(urls).toHaveLength(1);
      expect(urls![0]).toBe('https://example.com');
      
      // Phone detection
      const phonePattern = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g;
      const phones = text.match(phonePattern);
      expect(phones).toHaveLength(1);
      expect(phones![0]).toBe('************');
    });

    it('should handle edge cases', () => {
      // Empty text
      expect(''.split(/\s+/).filter(w => w.length > 0)).toHaveLength(0);
      
      // Very short text
      const shortText = 'Hi';
      expect(shortText.length).toBe(2);
      
      // Only whitespace
      const whitespaceText = '   \n\n\t  ';
      const trimmed = whitespaceText.trim();
      expect(trimmed).toBe('');
      
      // Single character
      const singleChar = 'A';
      expect(singleChar.length).toBe(1);
    });

    it('should validate configuration logic', () => {
      const config = {
        chunkSize: 1000,
        chunkOverlap: 200,
        minChunkSize: 50,
        maxChunkSize: 4000
      };
      
      const errors = [];
      
      if (config.chunkSize <= 0) {
        errors.push('Chunk size must be greater than 0');
      }
      
      if (config.chunkOverlap < 0) {
        errors.push('Chunk overlap cannot be negative');
      }
      
      if (config.chunkOverlap >= config.chunkSize) {
        errors.push('Chunk overlap must be less than chunk size');
      }
      
      if (config.minChunkSize > config.chunkSize) {
        errors.push('Minimum chunk size cannot be greater than chunk size');
      }
      
      if (config.maxChunkSize < config.chunkSize) {
        errors.push('Maximum chunk size cannot be less than chunk size');
      }
      
      expect(errors).toHaveLength(0);
    });
  });

  describe('Text Processing Utilities', () => {
    it('should estimate token count', () => {
      const text = 'This is a test text for token estimation.';
      // Rough approximation: 1 token ≈ 4 characters
      const estimatedTokens = Math.ceil(text.length / 4);
      expect(estimatedTokens).toBeGreaterThan(0);
      expect(estimatedTokens).toBe(Math.ceil(41 / 4)); // 11 tokens
    });

    it('should calculate optimal chunk size', () => {
      const tokenLimit = 1000;
      // Convert token limit to character count with buffer
      const optimalSize = Math.floor(tokenLimit * 3.5);
      expect(optimalSize).toBe(3500);
    });

    it('should handle Unicode normalization', () => {
      const text = 'Text with "smart quotes" and — dashes.';
      
      // Normalize Unicode form
      const normalized = text.normalize('NFC');
      expect(normalized).toBeDefined();
      
      // Replace smart quotes
      const withStandardQuotes = text.replace(/[\u2018\u2019\u201C\u201D]/g, (match) => {
        switch (match) {
          case '\u2018':
          case '\u2019':
            return "'";
          case '\u201C':
          case '\u201D':
            return '"';
          default:
            return match;
        }
      });
      
      expect(withStandardQuotes).toContain('"');
      expect(withStandardQuotes).not.toContain('"');
      expect(withStandardQuotes).not.toContain('"');
    });

    it('should calculate readability metrics', () => {
      const text = 'This is a simple test. It has short sentences. Easy to read.';
      const words = text.split(/\s+/).filter(w => w.length > 0);
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      
      const avgWordsPerSentence = words.length / sentences.length;
      expect(avgWordsPerSentence).toBeCloseTo(4, 1);
      
      const avgCharsPerWord = words.reduce((sum, word) => sum + word.replace(/[^\w]/g, '').length, 0) / words.length;
      expect(avgCharsPerWord).toBeGreaterThan(0);
    });
  });
});
