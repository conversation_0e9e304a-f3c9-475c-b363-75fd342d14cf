/**
 * Test script to demonstrate embedding functionality
 * This script shows how to use the embedding service with different providers
 */

import { config } from '../config/environment';
import { getEmbeddingService } from '../services/embeddingService';
import { logger } from '../utils/logger';

async function testEmbeddings() {
  try {
    console.log('🚀 Testing Embedding Generation Service');
    console.log('=====================================');
    
    // Get the embedding service
    const embeddingService = getEmbeddingService();
    
    console.log(`📊 Service Configuration:`);
    console.log(`   Provider: ${embeddingService.getServiceType()}`);
    console.log(`   Model: ${embeddingService.getModelName()}`);
    console.log(`   Dimension: ${embeddingService.getDimension()}`);
    console.log('');

    // Test single text embedding
    console.log('🔤 Testing single text embedding...');
    const singleText = 'This is a test sentence for embedding generation.';
    console.log(`   Input: "${singleText}"`);
    
    const singleEmbedding = await embeddingService.textEmbedding(singleText);
    console.log(`   ✅ Generated embedding with ${singleEmbedding.embedding.length} dimensions`);
    console.log(`   First 5 values: [${singleEmbedding.embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
    console.log('');

    // Test multiple text embeddings
    console.log('📝 Testing multiple text embeddings...');
    const multipleTexts = [
      'Hello world',
      'Machine learning is fascinating',
      'Natural language processing',
      'Vector embeddings represent semantic meaning'
    ];
    console.log(`   Input: ${multipleTexts.length} texts`);
    
    const multipleEmbeddings = await embeddingService.textsEmbeddings(multipleTexts);
    console.log(`   ✅ Generated ${multipleEmbeddings.length} embeddings`);
    
    multipleEmbeddings.forEach((embedding, index) => {
      console.log(`   Text ${index + 1}: [${embedding.embedding.slice(0, 3).map(v => v.toFixed(4)).join(', ')}...] (${embedding.embedding.length}D)`);
    });
    console.log('');

    // Test API response format
    console.log('🌐 Testing API response format...');
    const apiResponse = await embeddingService.createEmbeddingResponse(multipleTexts);
    console.log(`   ✅ API Response:`);
    console.log(`   Object: ${apiResponse.object}`);
    console.log(`   Model: ${apiResponse.model}`);
    console.log(`   Data count: ${apiResponse.data.length}`);
    console.log(`   Usage: ${apiResponse.usage?.total_tokens} tokens`);
    console.log('');

    // Test batch processing
    console.log('⚡ Testing batch processing...');
    const largeBatch = Array(15).fill(0).map((_, i) => `Test sentence number ${i + 1} for batch processing.`);
    console.log(`   Input: ${largeBatch.length} texts`);
    
    const batchEmbeddings = await embeddingService.batchEmbeddings(largeBatch, 5);
    console.log(`   ✅ Processed ${batchEmbeddings.length} embeddings in batches of 5`);
    console.log('');

    // Test caching
    console.log('💾 Testing caching functionality...');
    const cacheTestText = 'This text will be cached for faster retrieval.';
    
    // First call (should generate new embedding)
    console.log('   First call (cache miss)...');
    const start1 = Date.now();
    await embeddingService.textEmbedding(cacheTestText);
    const time1 = Date.now() - start1;
    console.log(`   ⏱️  Time: ${time1}ms`);
    
    // Second call (should use cache)
    console.log('   Second call (cache hit)...');
    const start2 = Date.now();
    await embeddingService.textEmbedding(cacheTestText);
    const time2 = Date.now() - start2;
    console.log(`   ⏱️  Time: ${time2}ms`);
    
    const speedup = time1 / time2;
    console.log(`   🚀 Cache speedup: ${speedup.toFixed(2)}x faster`);
    console.log('');

    // Get cache statistics
    console.log('📈 Cache Statistics:');
    const cacheStats = await embeddingService.getCacheStats();
    console.log(`   Hits: ${cacheStats.hits}`);
    console.log(`   Misses: ${cacheStats.misses}`);
    console.log(`   Size: ${cacheStats.size} entries`);
    const hitRate = cacheStats.hits / (cacheStats.hits + cacheStats.misses) * 100;
    console.log(`   Hit Rate: ${hitRate.toFixed(1)}%`);
    console.log('');

    console.log('✅ All embedding tests completed successfully!');
    console.log('=====================================');

  } catch (error) {
    console.error('❌ Error testing embeddings:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testEmbeddings().then(() => {
    console.log('🎉 Test completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
}

export { testEmbeddings };
