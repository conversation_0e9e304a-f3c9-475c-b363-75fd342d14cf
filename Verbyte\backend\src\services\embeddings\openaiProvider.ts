/**
 * OpenAI Embedding Provider
 * Uses OpenAI API for generating embeddings
 */

import OpenAI from 'openai';
import { config } from '../../config/environment';
import { logger } from '../../utils/logger';
import { EmbeddingProvider } from '../../types/document';

export class OpenAIEmbeddingProvider implements EmbeddingProvider {
  private openai: OpenAI;
  private readonly model: string;
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 1000; // 1 second
  private readonly maxBatchSize: number = 2048; // OpenAI's limit

  constructor() {
    if (!config.openai.apiKey) {
      throw new Error('OpenAI API key is required but not provided');
    }

    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });

    this.model = config.openai.embeddingModel;

    logger.info(`Initialized OpenAI embedding provider with model: ${this.model}`);
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (!texts || texts.length === 0) {
      throw new Error('No texts provided for embedding generation');
    }

    logger.debug(`Generating OpenAI embeddings for ${texts.length} texts`);

    try {
      // Process texts in batches to respect OpenAI's limits
      const results: number[][] = [];

      for (let i = 0; i < texts.length; i += this.maxBatchSize) {
        const batch = texts.slice(i, i + this.maxBatchSize);
        const batchResults = await this.processBatch(batch);
        results.push(...batchResults);
      }

      logger.debug(`Successfully generated ${results.length} OpenAI embeddings`);
      return results;

    } catch (error) {
      logger.error('Error generating OpenAI embeddings:', error);
      throw new Error(`OpenAI embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process a batch of texts using OpenAI's batch API
   */
  private async processBatch(texts: string[]): Promise<number[][]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        logger.debug(`OpenAI embedding batch attempt ${attempt} for ${texts.length} texts`);

        const response = await this.openai.embeddings.create({
          model: this.model,
          input: texts,
          encoding_format: 'float'
        });

        // Extract embeddings in the correct order
        const embeddings: number[][] = new Array(texts.length);
        
        for (const embeddingData of response.data) {
          embeddings[embeddingData.index] = embeddingData.embedding;
        }

        // Validate that all embeddings were received
        for (let i = 0; i < embeddings.length; i++) {
          if (!embeddings[i]) {
            throw new Error(`Missing embedding for index ${i}`);
          }
        }

        return embeddings;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        logger.warn(`OpenAI embedding batch attempt ${attempt} failed:`, lastError.message);

        // Check if it's a rate limit error
        if (lastError.message.includes('rate_limit') || lastError.message.includes('429')) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1);
          logger.info(`Rate limited, waiting ${delay}ms before retry...`);
          await this.sleep(delay);
        } else if (attempt < this.maxRetries) {
          // For other errors, use shorter delay
          await this.sleep(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate OpenAI embeddings after ${this.maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * Get the embedding dimension for the current model
   */
  getDimension(): number {
    // OpenAI embedding dimensions by model
    const dimensions: Record<string, number> = {
      'text-embedding-ada-002': 1536,
      'text-embedding-3-small': 1536,
      'text-embedding-3-large': 3072,
    };

    return dimensions[this.model] || 1536; // Default to ada-002 dimension
  }

  /**
   * Get the model name
   */
  getModelName(): string {
    return this.model;
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate API key and model availability
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      logger.debug('Validating OpenAI configuration...');
      
      // Test with a simple text
      const testText = "This is a test sentence for validation.";
      const response = await this.openai.embeddings.create({
        model: this.model,
        input: [testText],
        encoding_format: 'float'
      });

      if (response.data.length === 0) {
        throw new Error('No embedding data received');
      }

      logger.info('OpenAI configuration validated successfully');
      return true;

    } catch (error) {
      logger.error('OpenAI configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Get model information from OpenAI
   */
  async getModelInfo(): Promise<any> {
    try {
      // OpenAI doesn't provide a direct model info endpoint for embeddings
      // Return static information based on known models
      const modelInfo = {
        'text-embedding-ada-002': {
          dimension: 1536,
          maxTokens: 8191,
          pricing: { per1k: 0.0001 }
        },
        'text-embedding-3-small': {
          dimension: 1536,
          maxTokens: 8191,
          pricing: { per1k: 0.00002 }
        },
        'text-embedding-3-large': {
          dimension: 3072,
          maxTokens: 8191,
          pricing: { per1k: 0.00013 }
        }
      };

      return {
        model: this.model,
        ...modelInfo[this.model as keyof typeof modelInfo],
        provider: 'openai',
        status: 'available'
      };
    } catch (error) {
      logger.error('Error getting OpenAI model info:', error);
      throw error;
    }
  }

  /**
   * Check if the model supports batch processing
   */
  supportsBatchProcessing(): boolean {
    return true;
  }

  /**
   * Get recommended batch size for this provider
   */
  getRecommendedBatchSize(): number {
    return Math.min(this.maxBatchSize, 100); // Conservative batch size
  }

  /**
   * Estimate cost for embedding generation
   */
  estimateCost(textCount: number): { currency: string; amount: number } {
    const pricing: Record<string, number> = {
      'text-embedding-ada-002': 0.0001,
      'text-embedding-3-small': 0.00002,
      'text-embedding-3-large': 0.00013,
    };

    const pricePerThousand = pricing[this.model] || 0.0001;
    const estimatedTokens = textCount * 100; // Rough estimate: 100 tokens per text
    const cost = (estimatedTokens / 1000) * pricePerThousand;

    return {
      currency: 'USD',
      amount: Math.round(cost * 100000) / 100000 // Round to 5 decimal places
    };
  }

  /**
   * Get provider-specific metrics
   */
  getMetrics(): { 
    provider: string; 
    model: string; 
    dimension: number; 
    maxRetries: number;
    retryDelay: number;
    maxBatchSize: number;
  } {
    return {
      provider: 'openai',
      model: this.model,
      dimension: this.getDimension(),
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay,
      maxBatchSize: this.maxBatchSize
    };
  }

  /**
   * Get usage statistics (if available)
   */
  async getUsageStats(): Promise<any> {
    try {
      // OpenAI doesn't provide usage stats through the embeddings API
      // This would need to be tracked separately
      return {
        provider: 'openai',
        message: 'Usage statistics not available through API'
      };
    } catch (error) {
      logger.error('Error getting OpenAI usage stats:', error);
      return null;
    }
  }
}
