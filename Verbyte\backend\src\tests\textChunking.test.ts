import { describe, it, expect, beforeEach } from '@jest/globals';
import { TextChunkingService } from '@/services/textChunkingService';
import { TextPreprocessingService } from '@/services/textPreprocessingService';
import { MetadataExtractionService } from '@/services/metadataExtractionService';
import { ChunkingConfigService } from '@/config/chunkingConfig';
import { ChunkingStrategy, ChunkingConfig, TextPreprocessingOptions } from '@/types/document';

describe('Text Chunking Service', () => {
  let chunkingService: TextChunkingService;
  let preprocessingService: TextPreprocessingService;
  let metadataService: MetadataExtractionService;
  let configService: ChunkingConfigService;

  beforeEach(() => {
    chunkingService = TextChunkingService.getInstance();
    preprocessingService = TextPreprocessingService.getInstance();
    metadataService = MetadataExtractionService.getInstance();
    configService = ChunkingConfigService.getInstance();
  });

  describe('Character Chunking', () => {
    it('should create chunks with correct size and overlap', () => {
      const text = 'This is a test text that should be split into multiple chunks for testing purposes.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.CHARACTER,
        chunkSize: 30,
        chunkOverlap: 10,
        separators: [],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: false,
        minChunkSize: 5,
        maxChunkSize: 100
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks.length).toBeGreaterThan(1);
      expect(chunks[0].text.length).toBeLessThanOrEqual(30);
      expect(chunks[0].startIndex).toBe(0);
      expect(chunks[0].metadata?.strategy).toBe(ChunkingStrategy.CHARACTER);
    });

    it('should respect word boundaries when enabled', () => {
      const text = 'This is a test text with some longer words that should not be broken.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.CHARACTER,
        chunkSize: 25,
        chunkOverlap: 5,
        separators: [],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 5,
        maxChunkSize: 100
      };

      const chunks = chunkingService.chunkText(text, config);

      // Check that no chunk ends in the middle of a word
      chunks.forEach(chunk => {
        if (chunk.endIndex < text.length) {
          const nextChar = text[chunk.endIndex];
          expect(nextChar).toMatch(/\s/); // Should be whitespace
        }
      });
    });
  });

  describe('Recursive Character Chunking', () => {
    it('should split text using hierarchical separators', () => {
      const text = 'Paragraph 1.\n\nParagraph 2 with multiple sentences. This is another sentence.\n\nParagraph 3.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
        chunkSize: 40,
        chunkOverlap: 10,
        separators: ['\n\n', '\n', '. ', ' ', ''],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 5,
        maxChunkSize: 100
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks.length).toBeGreaterThan(1);
      chunks.forEach(chunk => {
        expect(chunk.text.length).toBeLessThanOrEqual(40);
        expect(chunk.metadata?.strategy).toBe(ChunkingStrategy.RECURSIVE_CHARACTER);
      });
    });

    it('should handle markdown-style separators', () => {
      const text = '# Heading 1\n\nSome content.\n\n## Heading 2\n\nMore content here.\n\n### Heading 3\n\nEven more content.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
        chunkSize: 50,
        chunkOverlap: 10,
        separators: ['\n## ', '\n### ', '\n\n', '\n', '. ', ' ', ''],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 5,
        maxChunkSize: 100
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks.length).toBeGreaterThan(1);
      // Should preserve heading structure
      expect(chunks.some(chunk => chunk.text.includes('# Heading 1'))).toBe(true);
    });
  });

  describe('Sentence Chunking', () => {
    it('should create sentence-based chunks with window size', () => {
      const text = 'First sentence. Second sentence. Third sentence. Fourth sentence. Fifth sentence.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.SENTENCE,
        chunkSize: 100,
        chunkOverlap: 1,
        separators: [],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        sentenceWindowSize: 2,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 5,
        maxChunkSize: 200
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks.length).toBeGreaterThan(1);
      chunks.forEach(chunk => {
        expect(chunk.metadata?.type).toBe('sentence');
        expect(chunk.metadata?.sentenceCount).toBeLessThanOrEqual(2);
      });
    });
  });

  describe('Paragraph Chunking', () => {
    it('should split text by paragraphs', () => {
      const text = 'First paragraph with some content.\n\nSecond paragraph with more content.\n\nThird paragraph.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.PARAGRAPH,
        chunkSize: 50,
        chunkOverlap: 10,
        separators: [],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 5,
        maxChunkSize: 100
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks.length).toBeGreaterThan(1);
      chunks.forEach(chunk => {
        expect(chunk.metadata?.strategy).toBe(ChunkingStrategy.PARAGRAPH);
      });
    });

    it('should fall back to recursive chunking for single paragraph', () => {
      const text = 'This is a single long paragraph without any paragraph breaks that should be handled by recursive chunking.';
      const config: ChunkingConfig = {
        strategy: ChunkingStrategy.PARAGRAPH,
        chunkSize: 30,
        chunkOverlap: 5,
        separators: ['\n\n', '\n', '. ', ' ', ''],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 5,
        maxChunkSize: 100
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks.length).toBeGreaterThan(1);
      // Should fall back to recursive chunking
      chunks.forEach(chunk => {
        expect(chunk.text.length).toBeLessThanOrEqual(30);
      });
    });
  });

  describe('Text Preprocessing', () => {
    it('should normalize whitespace correctly', () => {
      const text = 'Text   with    multiple     spaces\t\tand\ttabs\n\n\n\nand newlines.';
      const options: TextPreprocessingOptions = {
        normalizeWhitespace: true,
        removeExtraSpaces: true,
        preserveLineBreaks: true,
        removeControlCharacters: true,
        trimChunks: true
      };

      const processed = preprocessingService.preprocessText(text, options);

      expect(processed).not.toContain('   '); // No multiple spaces
      expect(processed).not.toContain('\t'); // No tabs
      expect(processed).not.toContain('\n\n\n'); // No excessive newlines
    });

    it('should remove control characters', () => {
      const text = 'Text\x00with\x01control\x02characters\x03.';
      const options: TextPreprocessingOptions = {
        removeControlCharacters: true,
        normalizeWhitespace: false,
        removeExtraSpaces: false,
        preserveLineBreaks: true,
        trimChunks: false
      };

      const processed = preprocessingService.preprocessText(text, options);

      expect(processed).toBe('Textwithcontrolcharacters.');
    });

    it('should normalize Unicode characters', () => {
      const text = 'Text with "smart quotes" and — dashes.';
      const options: TextPreprocessingOptions = {
        normalizeUnicode: true,
        normalizeWhitespace: false,
        removeExtraSpaces: false,
        preserveLineBreaks: true,
        removeControlCharacters: false,
        trimChunks: false
      };

      const processed = preprocessingService.preprocessText(text, options);

      expect(processed).toContain('"'); // Should convert smart quotes
      expect(processed).toContain('-'); // Should convert em dash
    });
  });

  describe('Metadata Extraction', () => {
    it('should extract basic text metrics', () => {
      const chunk = {
        id: 'test-chunk',
        text: 'This is a test sentence. This is another sentence.',
        startIndex: 0,
        endIndex: 50,
        metadata: {}
      };

      const enrichedChunk = metadataService.extractChunkMetadata(chunk);

      expect(enrichedChunk.metadata?.characterCount).toBe(50);
      expect(enrichedChunk.metadata?.wordCount).toBe(10);
      expect(enrichedChunk.metadata?.sentenceCount).toBe(2);
    });

    it('should detect content types', () => {
      const chunk = {
        id: 'test-chunk',
        text: 'Contact <NAME_EMAIL> or visit https://example.com for more info.',
        startIndex: 0,
        endIndex: 73,
        metadata: {}
      };

      const enrichedChunk = metadataService.extractChunkMetadata(chunk);

      expect(enrichedChunk.metadata?.hasEmails).toBe(true);
      expect(enrichedChunk.metadata?.hasUrls).toBe(true);
      expect(enrichedChunk.metadata?.contentType?.hasStructuredData).toBe(true);
    });

    it('should calculate quality scores', () => {
      const goodChunk = {
        id: 'good-chunk',
        text: 'This is a well-formed paragraph with proper sentences. It contains meaningful content and has good structure.',
        startIndex: 0,
        endIndex: 105,
        metadata: {}
      };

      const poorChunk = {
        id: 'poor-chunk',
        text: 'x',
        startIndex: 0,
        endIndex: 1,
        metadata: {}
      };

      const goodEnriched = metadataService.extractChunkMetadata(goodChunk);
      const poorEnriched = metadataService.extractChunkMetadata(poorChunk);

      expect(goodEnriched.metadata?.qualityScore).toBeGreaterThan(poorEnriched.metadata?.qualityScore);
    });
  });

  describe('Configuration Service', () => {
    it('should provide default configuration', () => {
      const config = configService.getDefaultConfig();

      expect(config.chunkSize).toBeGreaterThan(0);
      expect(config.chunkOverlap).toBeGreaterThanOrEqual(0);
      expect(config.chunkOverlap).toBeLessThan(config.chunkSize);
      expect(config.strategy).toBeDefined();
    });

    it('should provide document-specific configurations', () => {
      const pdfConfig = configService.getConfigForDocumentType('pdf');
      const csvConfig = configService.getConfigForDocumentType('csv');

      expect(pdfConfig.strategy).toBe(ChunkingStrategy.RECURSIVE_CHARACTER);
      expect(csvConfig.strategy).toBe(ChunkingStrategy.CHARACTER);
      expect(csvConfig.chunkSize).toBeGreaterThan(pdfConfig.chunkSize); // CSV should have larger chunks
    });

    it('should validate configurations correctly', () => {
      const validConfig: ChunkingConfig = {
        strategy: ChunkingStrategy.CHARACTER,
        chunkSize: 1000,
        chunkOverlap: 200,
        separators: [],
        keepSeparator: false,
        lengthFunction: (text: string) => text.length,
        preserveFormatting: true,
        respectWordBoundaries: true,
        minChunkSize: 50,
        maxChunkSize: 2000
      };

      const invalidConfig: ChunkingConfig = {
        ...validConfig,
        chunkSize: 100,
        chunkOverlap: 150 // Overlap greater than chunk size
      };

      const validResult = configService.validateConfig(validConfig);
      const invalidResult = configService.validateConfig(invalidConfig);

      expect(validResult.isValid).toBe(true);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty text', () => {
      const text = '';
      const config = configService.getDefaultConfig();

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks).toHaveLength(0);
    });

    it('should handle very short text', () => {
      const text = 'Hi';
      const config = configService.getDefaultConfig();

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks).toHaveLength(1);
      expect(chunks[0].text).toBe('Hi');
    });

    it('should handle text shorter than chunk size', () => {
      const text = 'This is a short text.';
      const config: ChunkingConfig = {
        ...configService.getDefaultConfig(),
        chunkSize: 1000
      };

      const chunks = chunkingService.chunkText(text, config);

      expect(chunks).toHaveLength(1);
      expect(chunks[0].text).toBe(text);
    });

    it('should handle text with only whitespace', () => {
      const text = '   \n\n\t  ';
      const config = configService.getDefaultConfig();

      const chunks = chunkingService.chunkText(text, config);

      // Should either be empty or contain minimal content after preprocessing
      expect(chunks.length).toBeLessThanOrEqual(1);
    });
  });
});
