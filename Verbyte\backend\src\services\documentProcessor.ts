import fs from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  ProcessingStage,
  TextExtractionResult,
  DocumentMetadata,
  ProcessingOptions,
  DocumentProcessorConfig,
  ProcessingError,
  DocumentProcessingError,
  IDocumentProcessor,
  BatchProcessingResult
} from '@/types/document';

// Import individual processors
import { PDFProcessor } from './processors/pdfProcessor';
import { DOCXProcessor } from './processors/docxProcessor';
import { CSVProcessor } from './processors/csvProcessor';
import { MarkdownProcessor } from './processors/markdownProcessor';
import { JSONProcessor } from './processors/jsonProcessor';
import { ImageProcessor } from './processors/imageProcessor';
import { PowerPointProcessor } from './processors/powerPointProcessor';
import { TextProcessor } from './processors/textProcessor';

export class DocumentProcessingService {
  private processors: Map<DocumentType, IDocumentProcessor>;
  private config: DocumentProcessorConfig;

  constructor(config?: Partial<DocumentProcessorConfig>) {
    this.config = {
      maxFileSize: 100 * 1024 * 1024, // 100MB default
      supportedTypes: Object.values(DocumentType),
      defaultOptions: {
        extractImages: false,
        preserveFormatting: true,
        includeMetadata: true,
        chunkSize: 1000,
        chunkOverlap: 200,
        ocrLanguage: 'eng',
        csvHasHeaders: true,
        maxFileSize: 100 * 1024 * 1024,
        timeout: 300000 // 5 minutes
      },
      ocrEnabled: true,
      tempDirectory: path.join(process.cwd(), 'temp'),
      cleanupAfterProcessing: true,
      ...config
    };

    this.processors = new Map();
    this.initializeProcessors();
  }

  /**
   * Initialize all document processors
   */
  private initializeProcessors(): void {
    try {
      this.processors.set(DocumentType.PDF, new PDFProcessor());
      this.processors.set(DocumentType.DOCX, new DOCXProcessor());
      this.processors.set(DocumentType.CSV, new CSVProcessor());
      this.processors.set(DocumentType.MARKDOWN, new MarkdownProcessor());
      this.processors.set(DocumentType.JSON, new JSONProcessor());
      this.processors.set(DocumentType.IMAGE, new ImageProcessor());
      this.processors.set(DocumentType.POWERPOINT, new PowerPointProcessor());
      this.processors.set(DocumentType.TEXT, new TextProcessor());

      logger.info('Document processors initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize document processors:', error);
      throw error;
    }
  }

  /**
   * Process a single document
   */
  async processDocument(
    filePath: string,
    options?: ProcessingOptions
  ): Promise<TextExtractionResult> {
    const startTime = Date.now();
    const mergedOptions = { ...this.config.defaultOptions, ...options };

    try {
      // Validate file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Get file stats
      const stats = fs.statSync(filePath);
      if (stats.size > mergedOptions.maxFileSize!) {
        throw new Error(`File too large: ${stats.size} bytes (max: ${mergedOptions.maxFileSize} bytes)`);
      }

      // Determine document type
      const documentType = this.getDocumentType(filePath);
      if (!documentType) {
        throw new Error(`Unsupported file type: ${path.extname(filePath)}`);
      }

      // Get appropriate processor
      const processor = this.processors.get(documentType);
      if (!processor) {
        throw new Error(`No processor available for document type: ${documentType}`);
      }

      logger.info(`Processing document: ${filePath} (type: ${documentType})`);

      // Process the document
      const result = await processor.process(filePath, mergedOptions);

      // Add processing time
      result.processingTime = Date.now() - startTime;

      logger.info(`Document processed successfully: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error(`Document processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Process multiple documents in batch
   */
  async processBatch(
    filePaths: string[],
    options?: ProcessingOptions
  ): Promise<BatchProcessingResult> {
    const startTime = Date.now();
    const results: TextExtractionResult[] = [];
    const errors: ProcessingError[] = [];

    logger.info(`Starting batch processing of ${filePaths.length} documents`);

    for (const filePath of filePaths) {
      try {
        const result = await this.processDocument(filePath, options);
        results.push(result);

        if (!result.success) {
          errors.push({
            code: DocumentProcessingError.EXTRACTION_FAILED,
            message: result.error || 'Processing failed',
            details: filePath,
            stage: ProcessingStage.EXTRACTING_TEXT,
            timestamp: new Date()
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push({
          code: DocumentProcessingError.UNKNOWN_ERROR,
          message: errorMessage,
          details: filePath,
          stage: ProcessingStage.EXTRACTING_TEXT,
          timestamp: new Date()
        });

        // Add failed result
        results.push({
          text: '',
          metadata: this.createErrorMetadata(filePath, errorMessage),
          processingTime: 0,
          success: false,
          error: errorMessage
        });
      }
    }

    const processingTime = Date.now() - startTime;
    const successfulFiles = results.filter(r => r.success).length;
    const failedFiles = results.length - successfulFiles;

    logger.info(`Batch processing completed: ${successfulFiles} successful, ${failedFiles} failed (${processingTime}ms)`);

    return {
      totalFiles: filePaths.length,
      successfulFiles,
      failedFiles,
      results,
      errors,
      processingTime
    };
  }

  /**
   * Get document type from file path
   */
  private getDocumentType(filePath: string): DocumentType | null {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypeMap: Record<string, DocumentType> = {
      '.pdf': DocumentType.PDF,
      '.docx': DocumentType.DOCX,
      '.doc': DocumentType.DOCX,
      '.csv': DocumentType.CSV,
      '.md': DocumentType.MARKDOWN,
      '.markdown': DocumentType.MARKDOWN,
      '.json': DocumentType.JSON,
      '.jpg': DocumentType.IMAGE,
      '.jpeg': DocumentType.IMAGE,
      '.png': DocumentType.IMAGE,
      '.gif': DocumentType.IMAGE,
      '.bmp': DocumentType.IMAGE,
      '.tiff': DocumentType.IMAGE,
      '.pptx': DocumentType.POWERPOINT,
      '.ppt': DocumentType.POWERPOINT,
      '.txt': DocumentType.TEXT,
      '.text': DocumentType.TEXT
    };

    return mimeTypeMap[ext] || null;
  }

  /**
   * Check if a file type is supported
   */
  isSupported(filePath: string): boolean {
    return this.getDocumentType(filePath) !== null;
  }

  /**
   * Get supported file extensions
   */
  getSupportedExtensions(): string[] {
    return [
      '.pdf', '.docx', '.doc', '.csv', '.md', '.markdown', '.json',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
      '.pptx', '.ppt', '.txt', '.text'
    ];
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): DocumentMetadata {
    const stats = fs.existsSync(filePath) ? fs.statSync(filePath) : null;

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'application/octet-stream',
      size: stats?.size || 0,
      type: DocumentType.TEXT,
      wordCount: 0,
      characterCount: 0,
      error
    };
  }

  /**
   * Get processor configuration
   */
  getConfig(): DocumentProcessorConfig {
    return { ...this.config };
  }

  /**
   * Update processor configuration
   */
  updateConfig(config: Partial<DocumentProcessorConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('Document processor configuration updated');
  }
}
