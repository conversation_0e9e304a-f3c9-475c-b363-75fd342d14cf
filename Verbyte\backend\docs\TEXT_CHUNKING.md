# Text Chunking and Processing Documentation

## Overview

The Verbyte backend implements a comprehensive text chunking and processing system that provides configurable text splitting algorithms equivalent to Python's SentenceWindowNodeParser and RecursiveCharacterTextSplitter from llama-index. This system is designed to maintain exact functional parity with the Python PrivateGPT implementation while offering enhanced features and performance optimizations.

## Architecture

### Core Services

1. **TextChunkingService** - Main service for text chunking with multiple strategies
2. **TextPreprocessingService** - Text cleaning and normalization utilities
3. **MetadataExtractionService** - Comprehensive metadata extraction for chunks
4. **ChunkingConfigService** - Configuration management with environment variable support

### Chunking Strategies

#### 1. Character Chunking (`ChunkingStrategy.CHARACTER`)
- Simple character-based splitting with fixed chunk sizes
- Supports word boundary respect
- Best for: Simple text processing, fixed-size requirements

#### 2. Recursive Character Chunking (`ChunkingStrategy.RECURSIVE_CHARACTER`)
- Hierarchical splitting using multiple separators
- Default separators: `['\n\n', '\n', '. ', ' ', '']`
- Equivalent to langchain's RecursiveCharacterTextSplitter
- Best for: General text processing, maintaining structure

#### 3. Sentence Chunking (`ChunkingStrategy.SENTENCE`)
- Sentence-based chunking with configurable window size
- Equivalent to llama-index's SentenceWindowNodeParser
- Best for: Question answering, semantic search

#### 4. Paragraph Chunking (`ChunkingStrategy.PARAGRAPH`)
- Paragraph-aware chunking that preserves boundaries
- Falls back to recursive chunking for single paragraphs
- Best for: Document analysis, content summarization

#### 5. Semantic Chunking (`ChunkingStrategy.SEMANTIC`)
- Placeholder for future semantic-based chunking
- Currently falls back to sentence chunking
- Best for: Advanced content analysis (future implementation)

## Configuration

### Default Configuration

```typescript
{
  strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
  chunkSize: 1000,
  chunkOverlap: 200,
  separators: ['\n\n', '\n', '. ', ' ', ''],
  keepSeparator: false,
  lengthFunction: (text: string) => text.length,
  sentenceWindowSize: 3,
  preserveFormatting: true,
  respectWordBoundaries: true,
  minChunkSize: 50,
  maxChunkSize: 4000
}
```

### Document-Specific Configurations

The system provides optimized configurations for different document types:

- **PDF**: Recursive character chunking with word boundary respect
- **DOCX**: Paragraph-based chunking with formatting preservation
- **TXT**: Paragraph-aware chunking
- **Markdown**: Recursive chunking with markdown-specific separators
- **CSV**: Character chunking with larger chunk sizes
- **PowerPoint**: Recursive chunking with slide separators
- **Images (OCR)**: Sentence chunking for extracted text

### Environment Variables

Configure chunking behavior using environment variables:

```bash
CHUNKING_DEFAULT_CHUNK_SIZE=1000
CHUNKING_DEFAULT_CHUNK_OVERLAP=200
CHUNKING_DEFAULT_STRATEGY=recursive_character
CHUNKING_MIN_CHUNK_SIZE=50
CHUNKING_MAX_CHUNK_SIZE=4000
CHUNKING_SENTENCE_WINDOW_SIZE=3
CHUNKING_RESPECT_WORD_BOUNDARIES=true
CHUNKING_PRESERVE_FORMATTING=true
PREPROCESSING_NORMALIZE_WHITESPACE=true
PREPROCESSING_REMOVE_CONTROL_CHARS=true
```

## Text Preprocessing

### Features

1. **Whitespace Normalization**
   - Normalize multiple spaces to single spaces
   - Handle tabs and line breaks appropriately
   - Preserve or collapse line structure as needed

2. **Unicode Normalization**
   - Convert smart quotes to standard quotes
   - Replace Unicode dashes with standard dashes
   - Remove zero-width characters
   - Normalize Unicode form (NFC)

3. **Control Character Removal**
   - Remove control characters except essential ones
   - Preserve newlines, carriage returns, and tabs

4. **Document-Specific Cleaning**
   - PDF: Remove page numbers, fix broken words
   - DOCX: Normalize line endings, clean formatting
   - HTML: Convert HTML entities
   - Markdown: Preserve structure while cleaning
   - CSV: Normalize delimiters and quotes

## Metadata Extraction

### Chunk Metadata

Each chunk includes comprehensive metadata:

```typescript
{
  // Basic metrics
  characterCount: number,
  wordCount: number,
  sentenceCount: number,
  paragraphCount: number,
  
  // Position information
  relativePosition: number,
  
  // Content analysis
  language: string,
  contentType: {
    type: string,
    entities: Record<string, number>,
    hasStructuredData: boolean
  },
  complexity: {
    averageWordsPerSentence: number,
    averageCharactersPerWord: number,
    uniqueWordRatio: number,
    complexityScore: number
  },
  
  // Quality metrics
  readability: number,
  density: number,
  qualityScore: number,
  
  // Relationships
  relationships: {
    previousChunk: string | null,
    nextChunk: string | null,
    semanticSimilarity: Array<{chunkId: string, similarity: number}>,
    topicalContinuity: number
  }
}
```

## API Endpoints

### POST /api/chunking/chunk-text
Chunk text using specified configuration.

**Request:**
```json
{
  "text": "Your text to chunk...",
  "config": {
    "strategy": "recursive_character",
    "chunkSize": 1000,
    "chunkOverlap": 200
  },
  "preprocessing": {
    "normalizeWhitespace": true,
    "removeControlCharacters": true
  },
  "sourceMetadata": {
    "fileName": "document.txt",
    "documentType": "text"
  },
  "extractMetadata": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "chunks": [...],
    "totalChunks": 5,
    "originalLength": 5000,
    "config": {...},
    "preprocessing": {...}
  }
}
```

### POST /api/chunking/preprocess-text
Preprocess text using specified options.

### GET /api/chunking/config/default
Get default chunking configuration.

### GET /api/chunking/config/document-type/:type
Get configuration for specific document type.

### POST /api/chunking/config/validate
Validate chunking configuration.

### POST /api/chunking/analyze-text
Analyze text and extract metadata without chunking.

### GET /api/chunking/strategies
Get available chunking strategies.

### POST /api/chunking/test-performance
Test chunking performance with different configurations.

## Usage Examples

### Basic Text Chunking

```typescript
import { TextChunkingService } from '@/services/textChunkingService';

const chunkingService = TextChunkingService.getInstance();

const chunks = chunkingService.chunkText(
  "Your text here...",
  {
    strategy: ChunkingStrategy.RECURSIVE_CHARACTER,
    chunkSize: 1000,
    chunkOverlap: 200
  }
);
```

### Document-Specific Chunking

```typescript
import { ChunkingConfigService } from '@/config/chunkingConfig';

const configService = ChunkingConfigService.getInstance();
const config = configService.getConfigForDocumentType('pdf');

const chunks = chunkingService.chunkText(text, config);
```

### Text Preprocessing

```typescript
import { TextPreprocessingService } from '@/services/textPreprocessingService';

const preprocessingService = TextPreprocessingService.getInstance();

const cleanedText = preprocessingService.preprocessText(text, {
  normalizeWhitespace: true,
  removeControlCharacters: true,
  normalizeUnicode: true
});
```

### Metadata Extraction

```typescript
import { MetadataExtractionService } from '@/services/metadataExtractionService';

const metadataService = MetadataExtractionService.getInstance();

const enrichedChunk = metadataService.extractChunkMetadata(
  chunk,
  sourceMetadata,
  fullText
);
```

## Performance Considerations

1. **Memory Usage**: Large documents are processed in streaming fashion to minimize memory footprint
2. **Caching**: Chunk configurations and preprocessing results can be cached
3. **Batch Processing**: Multiple documents can be processed in batches for efficiency
4. **Parallel Processing**: CPU-intensive operations can be parallelized

## Testing

Comprehensive test suite covers:
- All chunking strategies
- Edge cases (empty text, very short text, whitespace-only text)
- Configuration validation
- Performance benchmarks
- Metadata extraction accuracy
- Text preprocessing quality

Run tests with:
```bash
npm test -- textChunking.test.ts
```

## Migration from Python

The JavaScript implementation maintains exact functional parity with the Python version:

1. **SentenceWindowNodeParser** → `ChunkingStrategy.SENTENCE`
2. **RecursiveCharacterTextSplitter** → `ChunkingStrategy.RECURSIVE_CHARACTER`
3. **Default parameters** → Identical chunk sizes and overlaps
4. **Metadata structure** → Compatible with existing systems
5. **API compatibility** → Maintains same request/response formats

## Future Enhancements

1. **Semantic Chunking**: Implementation of true semantic-based chunking
2. **Token-Aware Chunking**: Direct token counting for LLM optimization
3. **Adaptive Chunking**: Dynamic chunk sizing based on content complexity
4. **Multi-language Support**: Enhanced language detection and processing
5. **Performance Optimizations**: Further memory and speed improvements
