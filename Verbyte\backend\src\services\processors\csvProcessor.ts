import fs from 'fs';
import path from 'path';
import <PERSON> from 'papapar<PERSON>';
import { logger } from '@/utils/logger';
import {
  DocumentType,
  TextExtractionResult,
  CSVMetadata,
  ProcessingOptions,
  IDocumentProcessor,
  TextChunk
} from '@/types/document';

export class CSVProcessor implements IDocumentProcessor {
  /**
   * Check if this processor supports the given MIME type
   */
  supports(mimeType: string): boolean {
    return mimeType === 'text/csv' ||
      mimeType === 'application/csv' ||
      mimeType.includes('csv');
  }

  /**
   * Get the document type this processor handles
   */
  getType(): DocumentType {
    return DocumentType.CSV;
  }

  /**
   * Process a CSV file and extract text content
   */
  async process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult> {
    const startTime = Date.now();

    try {
      logger.debug(`Processing CSV file: ${filePath}`);

      // Read the CSV file
      const csvContent = fs.readFileSync(filePath, 'utf-8');

      // Configure Papa Parse options
      const parseOptions: Papa.ParseConfig = {
        header: options?.csvHasHeaders ?? true,
        delimiter: options?.csvDelimiter || '',
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim(),
        transform: (value: string) => value.trim()
      };

      // Parse the CSV
      const parseResult = Papa.parse(csvContent, parseOptions);

      if (parseResult.errors.length > 0) {
        logger.warn(`CSV parsing warnings for ${filePath}:`, parseResult.errors);
      }

      // Extract metadata
      const metadata = await this.extractMetadata(filePath, parseResult, csvContent);

      // Convert CSV data to text
      const text = this.convertToText(parseResult.data, parseOptions.header || false);

      // Create chunks if requested
      const chunks = options?.chunkSize ?
        this.createChunks(text, parseResult.data, options.chunkSize, options.chunkOverlap || 0) :
        undefined;

      // Update metadata with extracted information
      metadata.wordCount = this.countWords(text);
      metadata.characterCount = text.length;

      const result: TextExtractionResult = {
        text,
        metadata,
        chunks,
        processingTime: Date.now() - startTime,
        success: true
      };

      logger.debug(`CSV processing completed: ${filePath} (${result.processingTime}ms)`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown CSV processing error';
      logger.error(`CSV processing failed: ${filePath}`, error);

      return {
        text: '',
        metadata: this.createErrorMetadata(filePath, errorMessage),
        processingTime: Date.now() - startTime,
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Extract metadata from CSV parse result
   */
  private async extractMetadata(filePath: string, parseResult: Papa.ParseResult<any>, csvContent: string): Promise<CSVMetadata> {
    try {
      const stats = fs.statSync(filePath);

      // Detect delimiter if not specified
      const delimiter = this.detectDelimiter(csvContent);

      // Get column information
      const hasHeaders = Array.isArray(parseResult.data[0]) ?
        false :
        Object.keys(parseResult.data[0] || {}).length > 0;

      const columns = hasHeaders && parseResult.data.length > 0 ?
        Object.keys(parseResult.data[0]) :
        [];

      const metadata: CSVMetadata = {
        filename: path.basename(filePath),
        originalName: path.basename(filePath),
        mimeType: 'text/csv',
        size: stats.size,
        type: DocumentType.CSV,
        delimiter,
        hasHeaders,
        rowCount: parseResult.data.length,
        columnCount: hasHeaders ? columns.length : (Array.isArray(parseResult.data[0]) ? parseResult.data[0].length : 0),
        columns: hasHeaders ? columns : undefined,
        encoding: 'utf-8',
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      return metadata;
    } catch (error) {
      logger.warn(`Failed to extract CSV metadata: ${filePath}`, error);
      return this.createBasicMetadata(filePath);
    }
  }

  /**
   * Convert CSV data to readable text
   */
  private convertToText(data: any[], hasHeaders: boolean): string {
    if (!data || data.length === 0) {
      return '';
    }

    const textParts: string[] = [];

    if (hasHeaders && typeof data[0] === 'object') {
      // Object format with headers
      const headers = Object.keys(data[0]);
      textParts.push(`CSV Data with columns: ${headers.join(', ')}\n`);

      data.forEach((row, index) => {
        const rowText = headers.map(header => `${header}: ${row[header] || ''}`).join(', ');
        textParts.push(`Row ${index + 1}: ${rowText}`);
      });
    } else {
      // Array format without headers
      textParts.push('CSV Data:\n');

      data.forEach((row, index) => {
        if (Array.isArray(row)) {
          textParts.push(`Row ${index + 1}: ${row.join(', ')}`);
        } else {
          textParts.push(`Row ${index + 1}: ${String(row)}`);
        }
      });
    }

    return textParts.join('\n');
  }

  /**
   * Create text chunks from CSV data
   */
  private createChunks(text: string, data: any[], chunkSize: number, overlap: number): TextChunk[] {
    const chunks: TextChunk[] = [];
    const lines = text.split('\n');
    let currentPos = 0;

    // Create chunks based on rows
    const rowsPerChunk = Math.max(1, Math.floor(chunkSize / 100)); // Estimate rows per chunk

    for (let i = 0; i < lines.length; i += rowsPerChunk) {
      const endIndex = Math.min(i + rowsPerChunk, lines.length);
      const chunkLines = lines.slice(i, endIndex);
      const chunkText = chunkLines.join('\n');

      chunks.push({
        id: `csv_chunk_${chunks.length}`,
        text: chunkText,
        startIndex: currentPos,
        endIndex: currentPos + chunkText.length,
        metadata: {
          source: 'csv',
          chunkIndex: chunks.length,
          startRow: i,
          endRow: endIndex - 1,
          rowCount: chunkLines.length
        }
      });

      currentPos += chunkText.length + 1; // +1 for newline
    }

    return chunks;
  }

  /**
   * Detect CSV delimiter
   */
  private detectDelimiter(csvContent: string): string {
    const delimiters = [',', ';', '\t', '|'];
    const sample = csvContent.split('\n').slice(0, 5).join('\n'); // Use first 5 lines

    let bestDelimiter = ',';
    let maxCount = 0;

    for (const delimiter of delimiters) {
      const count = (sample.match(new RegExp(`\\${delimiter}`, 'g')) || []).length;
      if (count > maxCount) {
        maxCount = count;
        bestDelimiter = delimiter;
      }
    }

    return bestDelimiter;
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Create basic metadata when full metadata extraction fails
   */
  private createBasicMetadata(filePath: string): CSVMetadata {
    const stats = fs.statSync(filePath);

    return {
      filename: path.basename(filePath),
      originalName: path.basename(filePath),
      mimeType: 'text/csv',
      size: stats.size,
      type: DocumentType.CSV,
      delimiter: ',',
      hasHeaders: true,
      rowCount: 0,
      columnCount: 0,
      encoding: 'utf-8',
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  }

  /**
   * Create error metadata for failed processing
   */
  private createErrorMetadata(filePath: string, error: string): CSVMetadata {
    const basicMetadata = this.createBasicMetadata(filePath);
    return {
      ...basicMetadata,
      error
    };
  }
}
